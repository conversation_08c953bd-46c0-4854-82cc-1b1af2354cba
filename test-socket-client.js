const { io } = require('socket.io-client');

// Connect to the server
const socket = io('http://localhost:8080');

console.log('🔌 Connecting to Socket.IO server...');

socket.on('connect', () => {
  console.log('✅ Connected to server with ID:', socket.id);

  // Test joining a game
  console.log('🎮 Joining game ZSAGB1 with player ID 1...');
  socket.emit('joinGame', {
    gameCode: 'ZSAGB1',
    playerId: 1
  });
});

socket.on('disconnect', () => {
  console.log('❌ Disconnected from server');
});

// Listen for game events
socket.on('GAME_STARTED', (event) => {
  console.log('🎯 GAME_STARTED event received:', JSON.stringify(event, null, 2));
});

socket.on('GAME_UPDATE', (event) => {
  console.log('🔄 GAME_UPDATE event received:', JSON.stringify(event, null, 2));
});

socket.on('PLAYER_JOINED', (event) => {
  console.log('👥 PLAYER_JOINED event received:', JSON.stringify(event, null, 2));
});

socket.on('CARD_PLAYED', (event) => {
  console.log('🃏 CARD_PLAYED event received:', JSON.stringify(event, null, 2));
});

socket.on('CARD_DRAWN', (event) => {
  console.log('📥 CARD_DRAWN event received:', JSON.stringify(event, null, 2));
});

socket.on('UNO_CALLED', (event) => {
  console.log('🗣️ UNO_CALLED event received:', JSON.stringify(event, null, 2));
});

socket.on('GAME_WON', (event) => {
  console.log('🏆 GAME_WON event received:', JSON.stringify(event, null, 2));
});

// Handle connection errors
socket.on('connect_error', (error) => {
  console.error('❌ Connection error:', error);
});

// Test game actions after a delay
setTimeout(() => {
  console.log('\n🎲 Testing game actions...');

  // Try to play a card (this should work if player 1 has a valid card)
  console.log('🃏 Attempting to play SKIP YELLOW card (ID: 16)...');
  socket.emit('playCard', {
    gameCode: 'ZSAGB1',
    playerId: 1,
    cardId: 16, // SKIP YELLOW card from player 1's hand
    chosenColor: null
  });

}, 2000);

setTimeout(() => {
  // Try to draw a card
  console.log('📥 Attempting to draw a card...');
  socket.emit('drawCard', {
    gameCode: 'ZSAGB1',
    playerId: 1
  });
}, 4000);

setTimeout(() => {
  // Try to call Uno
  console.log('🗣️ Attempting to call Uno...');
  socket.emit('callUno', {
    gameCode: 'ZSAGB1',
    playerId: 1
  });
}, 6000);

// Keep the client running for 10 seconds
setTimeout(() => {
  console.log('\n🔚 Test completed. Disconnecting...');
  socket.disconnect();
  process.exit(0);
}, 10000);
