name: flutter_uno_game
description: A multiplayer Uno game built with Flutter and Flame

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flame: ^1.10.1
  http: ^1.1.0
  socket_io_client: ^2.0.3+1
  provider: ^6.1.1
  shared_preferences: ^2.2.2
  uuid: ^4.2.1
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/sounds/
    
  fonts:
    - family: UnoFont
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
