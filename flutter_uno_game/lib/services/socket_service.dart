import 'dart:convert';
import 'package:socket_io_client/socket_io_client.dart' as IO;

class SocketService {
  static SocketService? _instance;
  static SocketService get instance => _instance ??= SocketService._();
  
  SocketService._();

  IO.Socket? _socket;
  bool _isConnected = false;

  bool get isConnected => _isConnected;

  void connect() {
    if (_socket != null && _isConnected) return;

    _socket = IO.io('http://localhost:8080', <String, dynamic>{
      'transports': ['websocket'],
      'autoConnect': false,
    });

    _socket!.connect();

    _socket!.onConnect((_) {
      print('🔌 Connected to Socket.IO server');
      _isConnected = true;
    });

    _socket!.onDisconnect((_) {
      print('❌ Disconnected from Socket.IO server');
      _isConnected = false;
    });

    _socket!.onConnectError((error) {
      print('❌ Connection error: $error');
      _isConnected = false;
    });
  }

  void disconnect() {
    if (_socket != null) {
      _socket!.disconnect();
      _socket = null;
      _isConnected = false;
    }
  }

  // Join a game room
  void joinGame(String gameCode, int playerId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('joinGame', {
        'gameCode': gameCode,
        'playerId': playerId,
      });
      print('🎮 Joining game room: $gameCode');
    }
  }

  // Play a card via socket
  void playCard(String gameCode, int playerId, int cardId, {String? chosenColor}) {
    if (_socket != null && _isConnected) {
      _socket!.emit('playCard', {
        'gameCode': gameCode,
        'playerId': playerId,
        'cardId': cardId,
        'chosenColor': chosenColor,
      });
    }
  }

  // Draw a card via socket
  void drawCard(String gameCode, int playerId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('drawCard', {
        'gameCode': gameCode,
        'playerId': playerId,
      });
    }
  }

  // Call Uno via socket
  void callUno(String gameCode, int playerId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('callUno', {
        'gameCode': gameCode,
        'playerId': playerId,
      });
    }
  }

  // Listen for game events
  void onGameStarted(Function(Map<String, dynamic>) callback) {
    _socket?.on('GAME_STARTED', (data) {
      print('🎯 Game started event received');
      callback(data);
    });
  }

  void onGameUpdate(Function(Map<String, dynamic>) callback) {
    _socket?.on('GAME_UPDATE', (data) {
      print('🔄 Game update event received');
      callback(data);
    });
  }

  void onPlayerJoined(Function(Map<String, dynamic>) callback) {
    _socket?.on('PLAYER_JOINED', (data) {
      print('👥 Player joined event received');
      callback(data);
    });
  }

  void onCardPlayed(Function(Map<String, dynamic>) callback) {
    _socket?.on('CARD_PLAYED', (data) {
      print('🃏 Card played event received');
      callback(data);
    });
  }

  void onCardDrawn(Function(Map<String, dynamic>) callback) {
    _socket?.on('CARD_DRAWN', (data) {
      print('📥 Card drawn event received');
      callback(data);
    });
  }

  void onUnoCalled(Function(Map<String, dynamic>) callback) {
    _socket?.on('UNO_CALLED', (data) {
      print('🗣️ Uno called event received');
      callback(data);
    });
  }

  void onGameWon(Function(Map<String, dynamic>) callback) {
    _socket?.on('GAME_WON', (data) {
      print('🏆 Game won event received');
      callback(data);
    });
  }

  // Remove all listeners
  void removeAllListeners() {
    _socket?.clearListeners();
  }

  // Remove specific listener
  void removeListener(String event) {
    _socket?.off(event);
  }
}
