import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_uno_game/models/player.dart';
import 'package:flutter_uno_game/models/game.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8080/api';
  
  // Create a new player (guest login)
  static Future<Player> createPlayer(String playerName) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/players'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'playerName': playerName}),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return Player.fromJson(data);
      } else {
        throw Exception('Failed to create player: ${response.body}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Create a new game
  static Future<Game> createGame(int playerId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/games'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'playerId': playerId,
          'maxPlayers': 2,
          'minPlayers': 2,
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return Game.fromJson(data);
      } else {
        throw Exception('Failed to create game: ${response.body}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Join an existing game
  static Future<Game> joinGame(String gameCode, int playerId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/games/join'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'gameCode': gameCode,
          'playerId': playerId,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return Game.fromJson(data);
      } else {
        throw Exception('Failed to join game: ${response.body}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get game details
  static Future<Game> getGame(String gameCode, int playerId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/games/$gameCode?playerId=$playerId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return Game.fromJson(data);
      } else {
        throw Exception('Failed to get game: ${response.body}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get available games
  static Future<List<Game>> getAvailableGames() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/games'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as List;
        return data.map((gameJson) => Game.fromJson(gameJson)).toList();
      } else {
        throw Exception('Failed to get available games: ${response.body}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Play a card
  static Future<Game> playCard(String gameCode, int playerId, int cardId, {String? chosenColor}) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/games/play-card'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'gameCode': gameCode,
          'playerId': playerId,
          'cardId': cardId,
          'chosenColor': chosenColor,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return Game.fromJson(data);
      } else {
        throw Exception('Failed to play card: ${response.body}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Draw a card
  static Future<Game> drawCard(String gameCode, int playerId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/games/draw-card'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'gameCode': gameCode,
          'playerId': playerId,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return Game.fromJson(data);
      } else {
        throw Exception('Failed to draw card: ${response.body}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Call Uno
  static Future<Game> callUno(String gameCode, int playerId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/games/call-uno'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'gameCode': gameCode,
          'playerId': playerId,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return Game.fromJson(data);
      } else {
        throw Exception('Failed to call Uno: ${response.body}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
}
