enum CardType {
  number,
  skip,
  reverse,
  drawTwo,
  wild,
  wildDrawFour,
}

enum CardColor {
  red,
  blue,
  green,
  yellow,
  wild,
}

class UnoCard {
  final int id;
  final CardType cardType;
  final CardColor color;
  final int? value;
  final int? positionInHand;
  final bool isTopCard;
  final bool isInDeck;

  UnoCard({
    required this.id,
    required this.cardType,
    required this.color,
    this.value,
    this.positionInHand,
    required this.isTopCard,
    required this.isInDeck,
  });

  factory UnoCard.fromJson(Map<String, dynamic> json) {
    CardType parseCardType(String type) {
      switch (type.toUpperCase()) {
        case 'NUMBER':
          return CardType.number;
        case 'SKIP':
          return CardType.skip;
        case 'REVERSE':
          return CardType.reverse;
        case 'DRAW_TWO':
          return CardType.drawTwo;
        case 'WILD':
          return CardType.wild;
        case 'WILD_DRAW_FOUR':
          return CardType.wildDrawFour;
        default:
          return CardType.number;
      }
    }

    CardColor parseCardColor(String color) {
      switch (color.toUpperCase()) {
        case 'RED':
          return CardColor.red;
        case 'BLUE':
          return CardColor.blue;
        case 'GREEN':
          return CardColor.green;
        case 'YELLOW':
          return CardColor.yellow;
        case 'WILD':
          return CardColor.wild;
        default:
          return CardColor.red;
      }
    }

    return UnoCard(
      id: json['id'] as int,
      cardType: parseCardType(json['cardType'] as String),
      color: parseCardColor(json['color'] as String),
      value: json['value'] as int?,
      positionInHand: json['positionInHand'] as int?,
      isTopCard: json['isTopCard'] as bool? ?? false,
      isInDeck: json['isInDeck'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cardType': cardType.name.toUpperCase(),
      'color': color.name.toUpperCase(),
      'value': value,
      'positionInHand': positionInHand,
      'isTopCard': isTopCard,
      'isInDeck': isInDeck,
    };
  }

  String get displayText {
    switch (cardType) {
      case CardType.number:
        return value?.toString() ?? '';
      case CardType.skip:
        return 'SKIP';
      case CardType.reverse:
        return 'REV';
      case CardType.drawTwo:
        return '+2';
      case CardType.wild:
        return 'WILD';
      case CardType.wildDrawFour:
        return '+4';
    }
  }

  bool get isWild => cardType == CardType.wild || cardType == CardType.wildDrawFour;
  bool get isActionCard => cardType != CardType.number;

  bool canPlayOn(UnoCard topCard) {
    // Wild cards can be played on anything
    if (isWild) return true;
    
    // Same color or same value/type
    if (color == topCard.color) return true;
    if (cardType == topCard.cardType && cardType == CardType.number) {
      return value == topCard.value;
    }
    if (cardType == topCard.cardType && cardType != CardType.number) {
      return true;
    }
    
    return false;
  }

  @override
  String toString() {
    return 'UnoCard(id: $id, type: $cardType, color: $color, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UnoCard && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
