import 'package:flutter_uno_game/models/player.dart';
import 'package:flutter_uno_game/models/card.dart';

enum GameStatus {
  waitingForPlayers,
  inProgress,
  finished,
  cancelled,
}

enum GameDirection {
  clockwise,
  counterclockwise,
}

class GamePlayer {
  final int id;
  final String playerName;
  final int playerOrder;
  final int cardsCount;
  final bool isActive;
  final bool hasCalledUno;
  final List<UnoCard>? hand;

  GamePlayer({
    required this.id,
    required this.playerName,
    required this.playerOrder,
    required this.cardsCount,
    required this.isActive,
    required this.hasCalledUno,
    this.hand,
  });

  factory GamePlayer.fromJson(Map<String, dynamic> json) {
    return GamePlayer(
      id: json['id'] as int,
      playerName: json['playerName'] as String,
      playerOrder: json['playerOrder'] as int,
      cardsCount: json['cardsCount'] as int,
      isActive: json['isActive'] as bool,
      hasCalledUno: json['hasCalledUno'] as bool,
      hand: json['hand'] != null
          ? (json['hand'] as List)
              .map((cardJson) => UnoCard.fromJson(cardJson))
              .toList()
          : null,
    );
  }
}

class Game {
  final int id;
  final String gameCode;
  final GameStatus status;
  final int maxPlayers;
  final int minPlayers;
  final int currentPlayerIndex;
  final GameDirection direction;
  final List<GamePlayer> players;
  final UnoCard? topCard;
  final Player createdBy;
  final DateTime createdAt;
  final DateTime? startedAt;
  final DateTime? finishedAt;
  final Player? winner;

  Game({
    required this.id,
    required this.gameCode,
    required this.status,
    required this.maxPlayers,
    required this.minPlayers,
    required this.currentPlayerIndex,
    required this.direction,
    required this.players,
    this.topCard,
    required this.createdBy,
    required this.createdAt,
    this.startedAt,
    this.finishedAt,
    this.winner,
  });

  factory Game.fromJson(Map<String, dynamic> json) {
    GameStatus parseStatus(String status) {
      switch (status.toUpperCase()) {
        case 'WAITING_FOR_PLAYERS':
          return GameStatus.waitingForPlayers;
        case 'IN_PROGRESS':
          return GameStatus.inProgress;
        case 'FINISHED':
          return GameStatus.finished;
        case 'CANCELLED':
          return GameStatus.cancelled;
        default:
          return GameStatus.waitingForPlayers;
      }
    }

    GameDirection parseDirection(String direction) {
      switch (direction.toUpperCase()) {
        case 'CLOCKWISE':
          return GameDirection.clockwise;
        case 'COUNTERCLOCKWISE':
          return GameDirection.counterclockwise;
        default:
          return GameDirection.clockwise;
      }
    }

    return Game(
      id: json['id'] as int,
      gameCode: json['gameCode'] as String,
      status: parseStatus(json['status'] as String),
      maxPlayers: json['maxPlayers'] as int,
      minPlayers: json['minPlayers'] as int,
      currentPlayerIndex: json['currentPlayerIndex'] as int,
      direction: parseDirection(json['direction'] as String),
      players: (json['players'] as List)
          .map((playerJson) => GamePlayer.fromJson(playerJson))
          .toList(),
      topCard: json['topCard'] != null
          ? UnoCard.fromJson(json['topCard'])
          : null,
      createdBy: Player.fromJson(json['createdBy']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      startedAt: json['startedAt'] != null
          ? DateTime.parse(json['startedAt'] as String)
          : null,
      finishedAt: json['finishedAt'] != null
          ? DateTime.parse(json['finishedAt'] as String)
          : null,
      winner: json['winner'] != null
          ? Player.fromJson(json['winner'])
          : null,
    );
  }

  bool get isWaitingForPlayers => status == GameStatus.waitingForPlayers;
  bool get isInProgress => status == GameStatus.inProgress;
  bool get isFinished => status == GameStatus.finished;
  
  GamePlayer? get currentPlayer {
    if (currentPlayerIndex >= 0 && currentPlayerIndex < players.length) {
      return players[currentPlayerIndex];
    }
    return null;
  }

  bool isPlayerTurn(int playerId) {
    final current = currentPlayer;
    return current != null && current.id == playerId;
  }
}
