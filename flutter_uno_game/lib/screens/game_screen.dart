import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_uno_game/providers/game_provider.dart';
import 'package:flutter_uno_game/providers/player_provider.dart';
import 'package:flutter_uno_game/utils/app_theme.dart';
import 'package:flutter_uno_game/models/game.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('UNO Game'),
        automaticallyImplyLeading: false,
        actions: [
          Consumer<GameProvider>(
            builder: (context, gameProvider, child) {
              return Text(
                gameProvider.currentGame?.gameCode ?? '',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.lightBackground,
              Color(0xFFE2E8F0),
            ],
          ),
        ),
        child: SafeArea(
          child: Consumer2<GameProvider, PlayerProvider>(
            builder: (context, gameProvider, playerProvider, child) {
              final game = gameProvider.currentGame;
              final currentPlayer = playerProvider.currentPlayer;
              
              if (game == null || currentPlayer == null) {
                return const Center(
                  child: Text('Game not found'),
                );
              }

              return Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // Opponent Info
                    _buildOpponentInfo(game, currentPlayer.id),
                    const SizedBox(height: 20),
                    
                    // Game Area
                    Expanded(
                      child: _buildGameArea(game),
                    ),
                    
                    // Current Player Info
                    _buildCurrentPlayerInfo(game, currentPlayer.id),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildOpponentInfo(Game game, int currentPlayerId) {
    final opponent = game.players.firstWhere(
      (player) => player.id != currentPlayerId,
      orElse: () => game.players.first,
    );

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: const BoxDecoration(
                color: AppTheme.primaryBlue,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 30,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    opponent.playerName.replaceFirst('Guest_', ''),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${opponent.cardsCount} cards',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (game.isPlayerTurn(opponent.id))
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  'Turn',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameArea(Game game) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Top Card
          if (game.topCard != null)
            Container(
              width: 120,
              height: 180,
              decoration: BoxDecoration(
                color: _getCardColor(game.topCard!.color),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.black, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  game.topCard!.displayText,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          const SizedBox(height: 20),
          Text(
            'Top Card',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentPlayerInfo(Game game, int currentPlayerId) {
    final currentGamePlayer = game.players.firstWhere(
      (player) => player.id == currentPlayerId,
      orElse: () => game.players.first,
    );

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: const BoxDecoration(
                    color: AppTheme.primaryGreen,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        currentGamePlayer.playerName.replaceFirst('Guest_', ''),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${currentGamePlayer.cardsCount} cards',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (game.isPlayerTurn(currentPlayerId))
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Text(
                      'Your Turn',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: game.isPlayerTurn(currentPlayerId) 
                        ? () => _drawCard(currentPlayerId)
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryBlue,
                    ),
                    child: const Text('Draw Card'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: currentGamePlayer.cardsCount == 1
                        ? () => _callUno(currentPlayerId)
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryRed,
                    ),
                    child: const Text('UNO!'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getCardColor(CardColor color) {
    switch (color) {
      case CardColor.red:
        return AppTheme.primaryRed;
      case CardColor.blue:
        return AppTheme.primaryBlue;
      case CardColor.green:
        return AppTheme.primaryGreen;
      case CardColor.yellow:
        return AppTheme.primaryYellow;
      case CardColor.wild:
        return Colors.black;
    }
  }

  void _drawCard(int playerId) {
    context.read<GameProvider>().drawCard(playerId);
  }

  void _callUno(int playerId) {
    context.read<GameProvider>().callUno(playerId);
  }
}
