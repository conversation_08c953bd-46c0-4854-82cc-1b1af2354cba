import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_uno_game/providers/game_provider.dart';
import 'package:flutter_uno_game/providers/player_provider.dart';
import 'package:flutter_uno_game/screens/flame_game_screen.dart';
import 'package:flutter_uno_game/utils/app_theme.dart';
import 'package:flutter_uno_game/models/game.dart';
import 'package:flutter_uno_game/models/card.dart';
import 'package:flutter_uno_game/widgets/card_animation.dart';

class WaitingRoomScreen extends StatefulWidget {
  final String playerName;
  final bool isOnline;

  const WaitingRoomScreen({
    Key? key,
    required this.playerName,
    this.isOnline = true,
  }) : super(key: key);

  @override
  State<WaitingRoomScreen> createState() => _WaitingRoomScreenState();
}

class _WaitingRoomScreenState extends State<WaitingRoomScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
    _fadeController.forward();

    // Listen for game state changes
    _listenForGameChanges();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _listenForGameChanges() {
    // Check game state periodically
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        final gameProvider = context.read<GameProvider>();
        final playerProvider = context.read<PlayerProvider>();

        if (gameProvider.currentGame != null &&
            playerProvider.currentPlayer != null) {
          gameProvider.refreshGame(playerProvider.currentPlayer!.id);

          // Check for card distribution
          if (gameProvider.cardDistributionData != null) {
            _handleCardDistribution(gameProvider, playerProvider);
            return; // Don't continue listening during distribution
          }

          // Check if game has started
          if (gameProvider.currentGame!.isInProgress) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (_) => const FlameGameScreen()),
            );
          } else {
            // Continue listening
            _listenForGameChanges();
          }
        }
      }
    });
  }

  void _handleCardDistribution(
      GameProvider gameProvider, PlayerProvider playerProvider) {
    final distributionData = gameProvider.cardDistributionData!;
    final currentPlayer = playerProvider.currentPlayer!;

    // Find player's cards from distribution data
    final players = distributionData['players'] as List;
    final playerData = players.firstWhere((p) => p['id'] == currentPlayer.id);
    final playerCards = (playerData['hand'] as List)
        .map((cardData) => UnoCard.fromJson(cardData as Map<String, dynamic>))
        .toList();

    // Find opponent cards
    final opponentCards = <String, int>{};
    for (final player in players) {
      if (player['id'] != currentPlayer.id) {
        opponentCards[player['playerName']] = player['cardsCount'] as int;
      }
    }

    // Start card distribution animation
    CardDistributionManager.startDistribution(
      context: context,
      playerCards: playerCards,
      opponentCards: opponentCards,
      onAllComplete: () {
        // Send completion event
        gameProvider.completeCardDistribution(currentPlayer.id);
        // Resume listening for game changes
        _listenForGameChanges();
      },
    );
  }

  void _copyGameCode() {
    final gameProvider = context.read<GameProvider>();
    if (gameProvider.currentGame != null) {
      Clipboard.setData(
          ClipboardData(text: gameProvider.currentGame!.gameCode));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Game code copied to clipboard!'),
          backgroundColor: AppTheme.primaryGreen,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _leaveGame() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Game'),
        content: const Text('Are you sure you want to leave this game?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GameProvider>().leaveGame();
              Navigator.of(context).pop();
            },
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final gameProvider = context.watch<GameProvider>();
    final gameCode = gameProvider.currentGame?.gameCode ?? '';

    return Scaffold(
      backgroundColor: const Color(0xFF232B32), // Dark background
      body: SafeArea(
        child: Stack(
          children: [
            // Game code at the top
            Positioned(
              top: 32,
              left: 0,
              right: 0,
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Game Code: ',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SelectableText(
                      gameCode,
                      style: TextStyle(
                        color: Colors.amber,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 2,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.copy, color: Colors.white70),
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: gameCode));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Game code copied!')),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            // Centered card and arrows
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 120,
                    height: 120,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Icon(Icons.sync, size: 100, color: Colors.grey[700]),
                        Container(
                          width: 70,
                          height: 100,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            image: const DecorationImage(
                              image: AssetImage('assets/images/card_back.png'),
                              fit: BoxFit.cover,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black26,
                                blurRadius: 8,
                                offset: Offset(0, 4),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  const Text(
                    "Waiting for other players...",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            // Player info and items button at the bottom
            Positioned(
              left: 0,
              right: 0,
              bottom: 48,
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 28,
                    backgroundImage: AssetImage('assets/images/5.png'),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        widget.playerName,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 18),
                      ),
                      const SizedBox(width: 8),
                      Icon(Icons.circle,
                          color: widget.isOnline ? Colors.green : Colors.grey,
                          size: 12),
                    ],
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: Show items dialog
                    },
                    icon: const Icon(Icons.star, color: Colors.amber),
                    label: const Text("ITEMS"),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black54,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
