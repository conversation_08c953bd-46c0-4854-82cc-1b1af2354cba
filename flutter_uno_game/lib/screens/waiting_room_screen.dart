import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_uno_game/providers/game_provider.dart';
import 'package:flutter_uno_game/providers/player_provider.dart';
import 'package:flutter_uno_game/screens/game_screen.dart';
import 'package:flutter_uno_game/utils/app_theme.dart';
import 'package:flutter_uno_game/models/game.dart';

class WaitingRoomScreen extends StatefulWidget {
  const WaitingRoomScreen({super.key});

  @override
  State<WaitingRoomScreen> createState() => _WaitingRoomScreenState();
}

class _WaitingRoomScreenState extends State<WaitingRoomScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
    _fadeController.forward();

    // Listen for game state changes
    _listenForGameChanges();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _listenForGameChanges() {
    // Check game state periodically
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        final gameProvider = context.read<GameProvider>();
        final playerProvider = context.read<PlayerProvider>();

        if (gameProvider.currentGame != null &&
            playerProvider.currentPlayer != null) {
          gameProvider.refreshGame(playerProvider.currentPlayer!.id);

          // Check if game has started
          if (gameProvider.currentGame!.isInProgress) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (_) => const GameScreen()),
            );
          } else {
            // Continue listening
            _listenForGameChanges();
          }
        }
      }
    });
  }

  void _copyGameCode() {
    final gameProvider = context.read<GameProvider>();
    if (gameProvider.currentGame != null) {
      Clipboard.setData(
          ClipboardData(text: gameProvider.currentGame!.gameCode));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Game code copied to clipboard!'),
          backgroundColor: AppTheme.primaryGreen,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _leaveGame() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Game'),
        content: const Text('Are you sure you want to leave this game?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GameProvider>().leaveGame();
              Navigator.of(context).pop();
            },
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Waiting Room'),
        actions: [
          IconButton(
            icon: const Icon(Icons.exit_to_app),
            onPressed: _leaveGame,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.lightBackground,
              Color(0xFFE2E8F0),
            ],
          ),
        ),
        child: SafeArea(
          child: Consumer<GameProvider>(
            builder: (context, gameProvider, child) {
              final game = gameProvider.currentGame;

              if (game == null) {
                return const Center(
                  child: Text('No game found'),
                );
              }

              return FadeTransition(
                opacity: _fadeAnimation,
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    children: [
                      const SizedBox(height: 40),

                      // Game Code Display
                      _buildGameCodeCard(game),
                      const SizedBox(height: 40),

                      // Waiting Animation
                      _buildWaitingAnimation(),
                      const SizedBox(height: 40),

                      // Players List
                      _buildPlayersList(game),
                      const SizedBox(height: 40),

                      // Status Text
                      _buildStatusText(game),

                      const Spacer(),

                      // Share Button
                      _buildShareButton(game),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildGameCodeCard(Game game) {
    return Card(
      elevation: 12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryRed.withValues(alpha: 0.1),
              AppTheme.primaryYellow.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: Column(
          children: [
            Text(
              'Game Code',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppTheme.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  game.gameCode,
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                        color: AppTheme.primaryRed,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 4,
                      ),
                ),
                const SizedBox(width: 16),
                IconButton(
                  onPressed: _copyGameCode,
                  icon: const Icon(Icons.copy),
                  style: IconButton.styleFrom(
                    backgroundColor: AppTheme.primaryRed.withValues(alpha: 0.1),
                    foregroundColor: AppTheme.primaryRed,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaitingAnimation() {
    return ScaleTransition(
      scale: _pulseAnimation,
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryBlue.withValues(alpha: 0.3),
              AppTheme.primaryGreen.withValues(alpha: 0.3),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryBlue.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: const Icon(
          Icons.hourglass_empty,
          size: 48,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildPlayersList(Game game) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Players (${game.players.length}/${game.maxPlayers})',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 16),
            ...game.players.map((player) => _buildPlayerItem(player)),
            if (game.players.length < game.maxPlayers) _buildEmptyPlayerSlot(),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerItem(GamePlayer player) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryGreen.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryGreen.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: const BoxDecoration(
              color: AppTheme.primaryGreen,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.person,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              player.playerName.replaceFirst('Guest_', ''),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
            ),
          ),
          const Icon(
            Icons.check_circle,
            color: AppTheme.primaryGreen,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyPlayerSlot() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.person_outline,
              color: Colors.grey,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              'Waiting for player...',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey,
                    fontStyle: FontStyle.italic,
                  ),
            ),
          ),
          const Icon(
            Icons.hourglass_empty,
            color: Colors.grey,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusText(Game game) {
    String statusText;
    Color statusColor;

    if (game.isWaitingForPlayers) {
      final playersNeeded = game.minPlayers - game.players.length;
      if (playersNeeded > 0) {
        statusText =
            'Waiting for $playersNeeded more player${playersNeeded > 1 ? 's' : ''}...';
        statusColor = AppTheme.primaryYellow;
      } else {
        statusText = 'Game will start soon!';
        statusColor = AppTheme.primaryGreen;
      }
    } else {
      statusText = 'Game is starting...';
      statusColor = AppTheme.primaryGreen;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        statusText,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildShareButton(Game game) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _copyGameCode,
        icon: const Icon(Icons.share),
        label: const Text('Share Game Code'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryBlue,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }
}
