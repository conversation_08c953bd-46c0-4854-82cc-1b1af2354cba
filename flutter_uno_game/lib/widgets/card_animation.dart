import 'package:flutter/material.dart';
import 'package:flutter_uno_game/utils/app_theme.dart';
import 'package:flutter_uno_game/models/card.dart';

class CardAnimationWidget extends StatefulWidget {
  final List<UnoCard> cards;
  final bool isPlayerCards;
  final VoidCallback? onAnimationComplete;
  final Duration animationDuration;

  const CardAnimationWidget({
    super.key,
    required this.cards,
    required this.isPlayerCards,
    this.onAnimationComplete,
    this.animationDuration = const Duration(milliseconds: 2000),
  });

  @override
  State<CardAnimationWidget> createState() => _CardAnimationWidgetState();
}

class _CardAnimationWidgetState extends State<CardAnimationWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<Offset>> _cardAnimations;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // Create animations for each card
    _cardAnimations = List.generate(widget.cards.length, (index) {
      final delay = index * 0.1; // Stagger the animations
      return Tween<Offset>(
        begin: const Offset(0, -2), // Start from deck position (top)
        end: Offset.zero, // End at final position
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Interval(
          delay,
          delay + 0.5,
          curve: Curves.easeOutBack,
        ),
      ));
    });

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    // Start animation
    _startAnimation();
  }

  void _startAnimation() async {
    await _controller.forward();
    if (widget.onAnimationComplete != null) {
      widget.onAnimationComplete!();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return SizedBox(
          height: widget.isPlayerCards ? 120 : 80,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(widget.cards.length, (index) {
              return SlideTransition(
                position: _cardAnimations[index],
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    child: _buildCard(widget.cards[index], index),
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }

  Widget _buildCard(UnoCard card, int index) {
    final cardColor = _getCardColor(card.color);
    
    return Container(
      width: widget.isPlayerCards ? 60 : 40,
      height: widget.isPlayerCards ? 90 : 60,
      decoration: BoxDecoration(
        color: widget.isPlayerCards ? cardColor : Colors.blue.shade800,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.black, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: widget.isPlayerCards
          ? Center(
              child: Text(
                card.displayText,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            )
          : null, // Opponent cards show back
    );
  }

  Color _getCardColor(CardColor color) {
    switch (color) {
      case CardColor.red:
        return AppTheme.primaryRed;
      case CardColor.blue:
        return AppTheme.primaryBlue;
      case CardColor.green:
        return AppTheme.primaryGreen;
      case CardColor.yellow:
        return AppTheme.primaryYellow;
      case CardColor.wild:
        return Colors.black;
    }
  }
}

// Function to animate player cards distribution
Future<void> animatePlayerCardDistribution({
  required BuildContext context,
  required List<UnoCard> cards,
  required VoidCallback onComplete,
}) async {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => Dialog(
      backgroundColor: Colors.transparent,
      child: CardAnimationWidget(
        cards: cards,
        isPlayerCards: true,
        onAnimationComplete: () {
          Navigator.of(context).pop();
          onComplete();
        },
      ),
    ),
  );
}

// Function to animate opponent cards distribution
Future<void> animateOpponentCardDistribution({
  required BuildContext context,
  required int cardCount,
  required String opponentName,
  required VoidCallback onComplete,
}) async {
  // Create dummy cards for opponent
  final dummyCards = List.generate(
    cardCount,
    (index) => UnoCard(
      id: index,
      cardType: CardType.number,
      color: CardColor.blue,
      value: 0,
      isTopCard: false,
      isInDeck: false,
    ),
  );

  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => Dialog(
      backgroundColor: Colors.transparent,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '$opponentName receiving cards...',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          CardAnimationWidget(
            cards: dummyCards,
            isPlayerCards: false,
            onAnimationComplete: () {
              Navigator.of(context).pop();
              onComplete();
            },
          ),
        ],
      ),
    ),
  );
}

// Card distribution manager
class CardDistributionManager {
  static Future<void> startDistribution({
    required BuildContext context,
    required List<UnoCard> playerCards,
    required Map<String, int> opponentCards,
    required VoidCallback onAllComplete,
  }) async {
    // First animate player cards
    await animatePlayerCardDistribution(
      context: context,
      cards: playerCards,
      onComplete: () {},
    );

    // Then animate opponent cards
    for (final entry in opponentCards.entries) {
      await animateOpponentCardDistribution(
        context: context,
        cardCount: entry.value,
        opponentName: entry.key,
        onComplete: () {},
      );
    }

    // All animations complete
    onAllComplete();
  }
}
