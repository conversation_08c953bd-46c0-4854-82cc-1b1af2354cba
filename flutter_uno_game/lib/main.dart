import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_uno_game/providers/game_provider.dart';
import 'package:flutter_uno_game/providers/player_provider.dart';
import 'package:flutter_uno_game/screens/login_screen.dart';
import 'package:flutter_uno_game/utils/app_theme.dart';

void main() {
  runApp(const UnoGameApp());
}

class UnoGameApp extends StatelessWidget {
  const UnoGameApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => PlayerProvider()),
        ChangeNotifierProvider(create: (_) => GameProvider()),
      ],
      child: MaterialApp(
        title: 'Uno Multiplayer Game',
        theme: AppTheme.lightTheme,
        home: const LoginScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
