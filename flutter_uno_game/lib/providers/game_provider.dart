import 'package:flutter/foundation.dart';
import 'package:flutter_uno_game/models/game.dart';
import 'package:flutter_uno_game/models/card.dart';
import 'package:flutter_uno_game/services/api_service.dart';
import 'package:flutter_uno_game/services/socket_service.dart';

class GameProvider extends ChangeNotifier {
  Game? _currentGame;
  bool _isLoading = false;
  String? _error;
  bool _isConnectedToSocket = false;

  Game? get currentGame => _currentGame;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isConnectedToSocket => _isConnectedToSocket;
  bool get isInGame => _currentGame != null;

  // Create a new game
  Future<bool> createGame(int playerId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final game = await ApiService.createGame(playerId);
      _currentGame = game;

      // Connect to socket and join game room
      _connectToSocket();
      SocketService.instance.joinGame(game.gameCode, playerId);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;

      // If player not found, it means the stored player data is stale
      if (e.toString().contains('Player not found')) {
        print(
            '🔄 Player not found - clearing stored data and forcing re-login');
        // Clear stored player data to force re-login
        // This will be handled by the UI to redirect to login
      }

      notifyListeners();
      return false;
    }
  }

  // Join an existing game
  Future<bool> joinGame(String gameCode, int playerId) async {
    if (gameCode.trim().isEmpty) {
      _error = 'Game code cannot be empty';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final game = await ApiService.joinGame(gameCode.toUpperCase(), playerId);
      _currentGame = game;

      // Connect to socket and join game room
      _connectToSocket();
      SocketService.instance.joinGame(game.gameCode, playerId);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Refresh game state
  Future<void> refreshGame(int playerId) async {
    if (_currentGame == null) return;

    try {
      final game = await ApiService.getGame(_currentGame!.gameCode, playerId);
      _currentGame = game;
      notifyListeners();
    } catch (e) {
      print('Error refreshing game: $e');
    }
  }

  // Connect to socket and set up listeners
  void _connectToSocket() {
    if (_isConnectedToSocket) return;

    SocketService.instance.connect();
    _isConnectedToSocket = true;

    // Set up event listeners
    SocketService.instance.onCardDistributionStarted((data) {
      print('Card distribution started: $data');
      // Handle card distribution animation
      if (_currentGame != null) {
        _handleCardDistribution(data);
      }
    });

    SocketService.instance.onGameStarted((data) {
      print('Game started: $data');
      // Refresh game state when game starts
      if (_currentGame != null) {
        _refreshGameFromSocket();
      }
    });

    SocketService.instance.onPlayerJoined((data) {
      print('Player joined: $data');
      // Refresh game state when player joins
      if (_currentGame != null) {
        _refreshGameFromSocket();
      }
    });

    SocketService.instance.onGameUpdate((data) {
      print('Game update: $data');
      // Update game state from socket data
      _refreshGameFromSocket();
    });

    SocketService.instance.onCardPlayed((data) {
      print('Card played: $data');
      _refreshGameFromSocket();
    });

    SocketService.instance.onCardDrawn((data) {
      print('Card drawn: $data');
      _refreshGameFromSocket();
    });

    SocketService.instance.onUnoCalled((data) {
      print('Uno called: $data');
      _refreshGameFromSocket();
    });

    SocketService.instance.onGameWon((data) {
      print('Game won: $data');
      _refreshGameFromSocket();
    });
  }

  // Refresh game state from API (called by socket events)
  void _refreshGameFromSocket() async {
    if (_currentGame == null) return;

    try {
      // We need a player ID to refresh - get it from current game players
      final currentPlayer = _currentGame!.players.first;
      await refreshGame(currentPlayer.id);
    } catch (e) {
      print('Error refreshing game from socket: $e');
    }
  }

  // Play a card
  Future<bool> playCard(int playerId, int cardId, {String? chosenColor}) async {
    if (_currentGame == null) return false;

    try {
      // Use socket for real-time play
      SocketService.instance.playCard(_currentGame!.gameCode, playerId, cardId,
          chosenColor: chosenColor);
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Draw a card
  Future<bool> drawCard(int playerId) async {
    if (_currentGame == null) return false;

    try {
      // Use socket for real-time play
      SocketService.instance.drawCard(_currentGame!.gameCode, playerId);
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Call Uno
  Future<bool> callUno(int playerId) async {
    if (_currentGame == null) return false;

    try {
      // Use socket for real-time play
      SocketService.instance.callUno(_currentGame!.gameCode, playerId);
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Leave game
  void leaveGame() {
    _currentGame = null;
    _error = null;

    // Disconnect from socket
    if (_isConnectedToSocket) {
      SocketService.instance.removeAllListeners();
      SocketService.instance.disconnect();
      _isConnectedToSocket = false;
    }

    notifyListeners();
  }

  // Handle card distribution
  void _handleCardDistribution(Map<String, dynamic> data) {
    print('🃏 Handling card distribution: $data');

    // Store the card distribution data for the UI to access
    _cardDistributionData = data;
    notifyListeners();

    // The UI will handle the animations and call completeCardDistribution when done
  }

  // Card distribution data for UI access
  Map<String, dynamic>? _cardDistributionData;
  Map<String, dynamic>? get cardDistributionData => _cardDistributionData;

  // Complete card distribution (called by UI after animations)
  void completeCardDistribution(int playerId) {
    if (_currentGame != null) {
      SocketService.instance
          .cardDistributionComplete(_currentGame!.gameCode, playerId);
      _cardDistributionData = null;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    if (_isConnectedToSocket) {
      SocketService.instance.removeAllListeners();
      SocketService.instance.disconnect();
    }
    super.dispose();
  }
}
