import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_uno_game/models/player.dart';
import 'package:flutter_uno_game/services/api_service.dart';

class PlayerProvider extends ChangeNotifier {
  Player? _currentPlayer;
  bool _isLoading = false;
  String? _error;

  Player? get currentPlayer => _currentPlayer;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _currentPlayer != null;

  // Load player from local storage
  Future<void> loadPlayer() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final playerData = prefs.getString('current_player');
      
      if (playerData != null) {
        final playerJson = Map<String, dynamic>.from(
          // Simple JSON parsing for stored player data
          {
            'id': prefs.getInt('player_id') ?? 0,
            'playerName': prefs.getString('player_name') ?? '',
            'coins': prefs.getInt('player_coins') ?? 1000,
          }
        );
        _currentPlayer = Player.fromJson(playerJson);
        notifyListeners();
      }
    } catch (e) {
      print('Error loading player: $e');
    }
  }

  // Save player to local storage
  Future<void> _savePlayer(Player player) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('player_id', player.id);
      await prefs.setString('player_name', player.playerName);
      await prefs.setInt('player_coins', player.coins);
      await prefs.setString('current_player', 'saved');
    } catch (e) {
      print('Error saving player: $e');
    }
  }

  // Login as guest
  Future<bool> loginAsGuest(String playerName) async {
    if (playerName.trim().isEmpty) {
      _error = 'Player name cannot be empty';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Add "Guest_" prefix and timestamp to make it unique
      final uniqueName = 'Guest_${playerName}_${DateTime.now().millisecondsSinceEpoch}';
      
      final player = await ApiService.createPlayer(uniqueName);
      _currentPlayer = player;
      await _savePlayer(player);
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_player');
      await prefs.remove('player_id');
      await prefs.remove('player_name');
      await prefs.remove('player_coins');
      
      _currentPlayer = null;
      _error = null;
      notifyListeners();
    } catch (e) {
      print('Error during logout: $e');
    }
  }

  // Update player data
  void updatePlayer(Player player) {
    _currentPlayer = player;
    _savePlayer(player);
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
