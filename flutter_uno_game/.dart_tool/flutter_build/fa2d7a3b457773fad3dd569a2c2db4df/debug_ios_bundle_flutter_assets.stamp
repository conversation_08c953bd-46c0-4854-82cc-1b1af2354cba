{"inputs": ["/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/engine.stamp", "/Users/<USER>/Documents/test-node/flutter_uno_game/.dart_tool/flutter_build/fa2d7a3b457773fad3dd569a2c2db4df/app.dill", "/Users/<USER>/Documents/test-node/flutter_uno_game/.dart_tool/flutter_build/fa2d7a3b457773fad3dd569a2c2db4df/App.framework/App", "/Users/<USER>/Documents/test-node/flutter_uno_game/pubspec.yaml", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter_tools/lib/src/build_system/tools/shader_compiler.dart", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/engine.stamp", "/Users/<USER>/Documents/test-node/flutter_uno_game/pubspec.yaml", "/Users/<USER>/Documents/test-node/flutter_uno_game/ios/Runner/Info.plist", "/Users/<USER>/Documents/test-node/flutter_uno_game/ios/Flutter/AppFrameworkInfo.plist", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/Documents/test-node/flutter_uno_game/.dart_tool/flutter_build/fa2d7a3b457773fad3dd569a2c2db4df/native_assets.json", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flame-1.30.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-3.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-3.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ordered_set-8.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/pkg/sky_engine/LICENSE", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/LICENSE", "/Users/<USER>/Documents/test-node/flutter_uno_game/DOES_NOT_EXIST_RERUN_FOR_WILDCARD996824672"], "outputs": ["/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/vm_snapshot_data", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/kernel_blob.bin", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/App", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/Info.plist", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NOTICES.Z", "/Users/<USER>/Documents/test-node/flutter_uno_game/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NativeAssetsManifest.json"]}