# Flutter UNO Multiplayer Game

A real-time multiplayer UNO card game built with Flutter and Flame, connecting to a Node.js backend with Socket.IO.

## Features

- **Guest Login**: Quick login as a guest player
- **Create Game**: Start a new game and get a 6-digit game code
- **Join Game**: Join an existing game using a game code
- **Real-time Multiplayer**: Live gameplay with Socket.IO
- **Waiting Room**: See players joining and game status
- **Game Interface**: Play cards, draw cards, call UNO
- **Beautiful UI**: Modern design with animations

## Project Structure

```
flutter_uno_game/
├── lib/
│   ├── main.dart                 # App entry point
│   ├── models/                   # Data models
│   │   ├── player.dart          # Player model
│   │   ├── game.dart            # Game model
│   │   └── card.dart            # Card model
│   ├── providers/               # State management
│   │   ├── player_provider.dart # Player state
│   │   └── game_provider.dart   # Game state
│   ├── services/                # API and Socket services
│   │   ├── api_service.dart     # REST API calls
│   │   └── socket_service.dart  # Socket.IO client
│   ├── screens/                 # UI screens
│   │   ├── login_screen.dart    # Guest login
│   │   ├── home_screen.dart     # Create/Join game
│   │   ├── waiting_room_screen.dart # Waiting for players
│   │   └── game_screen.dart     # Game interface
│   └── utils/
│       └── app_theme.dart       # App theme and colors
├── assets/                      # Images, sounds, fonts
└── pubspec.yaml                # Dependencies
```

## Backend Integration

This Flutter app connects to the Node.js UNO backend running on `localhost:8080`:

- **REST API**: Player creation, game management
- **Socket.IO**: Real-time game events and updates
- **PostgreSQL**: Game state persistence

## Getting Started

### Prerequisites

1. **Flutter SDK** (3.10.0 or higher)
2. **Node.js UNO Backend** running on port 8080
3. **PostgreSQL** database configured

### Installation

1. **Navigate to the Flutter project:**
   ```bash
   cd flutter_uno_game
   ```

2. **Install dependencies:**
   ```bash
   flutter pub get
   ```

3. **Ensure backend is running:**
   ```bash
   # In the parent directory
   npm start
   ```

4. **Run the Flutter app:**
   ```bash
   flutter run
   ```

## Game Flow

1. **Login Screen**: Enter your name to play as a guest
2. **Home Screen**: Choose to create a new game or join with a code
3. **Waiting Room**: See game code, players joining, and wait for game to start
4. **Game Screen**: Play UNO with real-time updates

## Key Dependencies

- `flame: ^1.10.1` - Game engine (for future card animations)
- `http: ^1.1.0` - REST API calls
- `socket_io_client: ^2.0.3+1` - Real-time communication
- `provider: ^6.1.1` - State management
- `shared_preferences: ^2.2.2` - Local storage

## Configuration

### Backend URL
Update the API base URL in `lib/services/api_service.dart` and `lib/services/socket_service.dart`:

```dart
static const String baseUrl = 'http://your-backend-url:8080/api';
```

### Socket.IO URL
```dart
_socket = IO.io('http://your-backend-url:8080', ...);
```

## Development Notes

- The app uses Provider for state management
- Socket.IO handles real-time game updates
- Local storage persists player login
- Material Design 3 with custom UNO theme
- Responsive design for different screen sizes

## Future Enhancements

- Card animations with Flame engine
- Sound effects and music
- Player avatars and customization
- Tournament mode
- AI opponents
- Push notifications

## Troubleshooting

### Common Issues

1. **Connection Error**: Ensure backend is running on correct port
2. **Socket Issues**: Check firewall and network connectivity
3. **Build Errors**: Run `flutter clean && flutter pub get`

### Debug Mode

Enable debug logging in socket service:
```dart
_socket!.onConnect((_) {
  print('🔌 Connected to Socket.IO server');
});
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is part of the UNO Multiplayer Game system.
