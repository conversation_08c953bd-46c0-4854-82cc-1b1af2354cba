{"name": "uno-backend-node", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "pg": "^8.16.3", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1"}}