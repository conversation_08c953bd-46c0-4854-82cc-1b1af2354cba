require('dotenv').config();
const express = require('express');
const http = require('http');
const cors = require('cors');
const { Server } = require('socket.io');

// Import database configuration
const { sequelize, testConnection } = require('./config/database');

// Import models
const { Player, Game, GamePlayer, Card } = require('./models');

// Import routes
const playerRoutes = require('./routes/playerRoutes');
const gameRoutes = require('./routes/gameRoutes');

// Import services
const { setupSocket } = require('./services/socketService');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: { 
    origin: process.env.CORS_ALLOWED_ORIGINS?.split(',') || '*',
    methods: ['GET', 'POST']
  }
});

app.use(cors());
app.use(express.json());

// Basic health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Uno Backend Server is running with PostgreSQL',
    timestamp: new Date().toISOString()
  });
});

// API routes
app.use('/api/players', playerRoutes);
app.use('/api/games', gameRoutes);

// Setup Socket.IO
setupSocket(io);

const PORT = process.env.PORT || 8080; // Use environment variable or default to 8080

// Start server with database connection
const startServer = async () => {
  try {
    console.log('🚀 Starting Uno Backend Server...');
    
    // Test database connection
    const dbConnected = await testConnection();
    
    if (dbConnected) {
      // Force recreate all tables to fix schema issues
      try {
        await sequelize.sync({ force: true });
        console.log('✅ Database tables recreated successfully');
      } catch (syncError) {
        console.log('⚠️  Database sync failed, starting server anyway:', syncError.message);
      }
    } else {
      console.log('⚠️  Starting server without database connection');
    }
    
    server.listen(PORT, () => {
      console.log(`🚀 Uno Backend Server running on port ${PORT}`);
      console.log(`📡 WebSocket server ready for real-time communication`);
      console.log(`🌐 CORS enabled for: ${process.env.CORS_ALLOWED_ORIGINS || '*'}`);
      console.log(`🔗 API endpoints available at http://localhost:${PORT}/api`);
      console.log(`🔗 WebSocket endpoint available at ws://localhost:${PORT}`);
      console.log(`🗄️  Database: PostgreSQL (testnode)`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
