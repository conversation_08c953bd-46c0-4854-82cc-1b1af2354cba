const express = require('express');
const router = express.Router();
const Game = require('../models/Game');
const Player = require('../models/Player');
const GamePlayer = require('../models/GamePlayer');

// GET /api/games - Get all games
router.get('/', async (req, res) => {
  try {
    const games = await Game.findAll({
      include: [
        {
          model: Player,
          as: 'host',
          attributes: ['id', 'username']
        },
        {
          model: Player,
          as: 'players',
          through: { attributes: [] },
          attributes: ['id', 'username']
        }
      ]
    });
    
    res.json({
      message: 'Games retrieved successfully',
      status: 'success',
      data: games
    });
  } catch (error) {
    console.error('Error fetching games:', error);
    res.status(500).json({
      message: 'Failed to fetch games',
      status: 'error',
      error: error.message
    });
  }
});

// POST /api/games - Create a new game
router.post('/', async (req, res) => {
  try {
    const { name, maxPlayers, hostId } = req.body;
    
    if (!name || !hostId) {
      return res.status(400).json({
        message: 'Game name and host ID are required',
        status: 'error'
      });
    }

    // Check if host exists
    const host = await Player.findByPk(hostId);
    if (!host) {
      return res.status(404).json({
        message: 'Host player not found',
        status: 'error'
      });
    }

    const game = await Game.create({
      name,
      maxPlayers: maxPlayers || 4,
      hostId: parseInt(hostId),
      status: 'waiting',
      currentPlayerIndex: 0
    });

    // Add host as first player
    await GamePlayer.create({
      gameId: game.id,
      playerId: hostId,
      position: 0
    });

    res.status(201).json({
      message: 'Game created successfully',
      status: 'success',
      data: game
    });
  } catch (error) {
    console.error('Error creating game:', error);
    res.status(500).json({
      message: 'Failed to create game',
      status: 'error',
      error: error.message
    });
  }
});

// GET /api/games/:id - Get game by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const game = await Game.findByPk(id, {
      include: [
        {
          model: Player,
          as: 'host',
          attributes: ['id', 'username']
        },
        {
          model: Player,
          as: 'players',
          through: { attributes: [] },
          attributes: ['id', 'username']
        }
      ]
    });
    
    if (!game) {
      return res.status(404).json({
        message: 'Game not found',
        status: 'error'
      });
    }

    res.json({
      message: `Game retrieved successfully`,
      status: 'success',
      data: game
    });
  } catch (error) {
    console.error('Error fetching game:', error);
    res.status(500).json({
      message: 'Failed to fetch game',
      status: 'error',
      error: error.message
    });
  }
});

// PUT /api/games/:id - Update game
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, status } = req.body;
    
    const game = await Game.findByPk(id);
    
    if (!game) {
      return res.status(404).json({
        message: 'Game not found',
        status: 'error'
      });
    }

    await game.update({
      name: name || game.name,
      status: status || game.status
    });

    res.json({
      message: `Game updated successfully`,
      status: 'success',
      data: game
    });
  } catch (error) {
    console.error('Error updating game:', error);
    res.status(500).json({
      message: 'Failed to update game',
      status: 'error',
      error: error.message
    });
  }
});

// DELETE /api/games/:id - Delete game
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const game = await Game.findByPk(id);
    
    if (!game) {
      return res.status(404).json({
        message: 'Game not found',
        status: 'error'
      });
    }

    await game.destroy();

    res.json({
      message: `Game deleted successfully`,
      status: 'success'
    });
  } catch (error) {
    console.error('Error deleting game:', error);
    res.status(500).json({
      message: 'Failed to delete game',
      status: 'error',
      error: error.message
    });
  }
});

// POST /api/games/:id/join - Join a game
router.post('/:id/join', async (req, res) => {
  try {
    const { id } = req.params;
    const { playerId } = req.body;
    
    if (!playerId) {
      return res.status(400).json({
        message: 'Player ID is required',
        status: 'error'
      });
    }

    // Check if game exists
    const game = await Game.findByPk(id);
    if (!game) {
      return res.status(404).json({
        message: 'Game not found',
        status: 'error'
      });
    }

    // Check if player exists
    const player = await Player.findByPk(playerId);
    if (!player) {
      return res.status(404).json({
        message: 'Player not found',
        status: 'error'
      });
    }

    // Check if player is already in the game
    const existingGamePlayer = await GamePlayer.findOne({
      where: { gameId: id, playerId }
    });

    if (existingGamePlayer) {
      return res.status(400).json({
        message: 'Player is already in this game',
        status: 'error'
      });
    }

    // Check if game is full
    const playerCount = await GamePlayer.count({ where: { gameId: id } });
    if (playerCount >= game.maxPlayers) {
      return res.status(400).json({
        message: 'Game is full',
        status: 'error'
      });
    }

    // Add player to game
    await GamePlayer.create({
      gameId: parseInt(id),
      playerId: parseInt(playerId),
      position: playerCount
    });

    res.json({
      message: `Player joined game successfully`,
      status: 'success',
      data: {
        gameId: parseInt(id),
        playerId: parseInt(playerId)
      }
    });
  } catch (error) {
    console.error('Error joining game:', error);
    res.status(500).json({
      message: 'Failed to join game',
      status: 'error',
      error: error.message
    });
  }
});

// POST /api/games/:id/start - Start a game
router.post('/:id/start', async (req, res) => {
  try {
    const { id } = req.params;
    
    const game = await Game.findByPk(id);
    
    if (!game) {
      return res.status(404).json({
        message: 'Game not found',
        status: 'error'
      });
    }

    // Check if game has enough players
    const playerCount = await GamePlayer.count({ where: { gameId: id } });
    if (playerCount < 2) {
      return res.status(400).json({
        message: 'Need at least 2 players to start the game',
        status: 'error'
      });
    }

    await game.update({
      status: 'playing'
    });

    res.json({
      message: `Game started successfully`,
      status: 'success',
      data: {
        gameId: parseInt(id),
        status: 'playing'
      }
    });
  } catch (error) {
    console.error('Error starting game:', error);
    res.status(500).json({
      message: 'Failed to start game',
      status: 'error',
      error: error.message
    });
  }
});

module.exports = router; 