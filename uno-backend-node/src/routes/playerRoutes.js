const express = require('express');
const router = express.Router();
const Player = require('../models/Player');

// GET /api/players - Get all players
router.get('/', async (req, res) => {
  try {
    const players = await Player.findAll();
    res.json({
      message: 'Players retrieved successfully',
      status: 'success',
      data: players
    });
  } catch (error) {
    console.error('Error fetching players:', error);
    res.status(500).json({
      message: 'Failed to fetch players',
      status: 'error',
      error: error.message
    });
  }
});

// POST /api/players - Create a new player
router.post('/', async (req, res) => {
  try {
    const { username, email } = req.body;
    
    if (!username) {
      return res.status(400).json({
        message: 'Username is required',
        status: 'error'
      });
    }

    const player = await Player.create({
      username,
      email: email || null
    });

    res.status(201).json({
      message: 'Player created successfully',
      status: 'success',
      data: player
    });
  } catch (error) {
    console.error('Error creating player:', error);
    res.status(500).json({
      message: 'Failed to create player',
      status: 'error',
      error: error.message
    });
  }
});

// GET /api/players/:id - Get player by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const player = await Player.findByPk(id);
    
    if (!player) {
      return res.status(404).json({
        message: 'Player not found',
        status: 'error'
      });
    }

    res.json({
      message: `Player retrieved successfully`,
      status: 'success',
      data: player
    });
  } catch (error) {
    console.error('Error fetching player:', error);
    res.status(500).json({
      message: 'Failed to fetch player',
      status: 'error',
      error: error.message
    });
  }
});

// PUT /api/players/:id - Update player
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email } = req.body;
    
    const player = await Player.findByPk(id);
    
    if (!player) {
      return res.status(404).json({
        message: 'Player not found',
        status: 'error'
      });
    }

    await player.update({
      username: username || player.username,
      email: email || player.email
    });

    res.json({
      message: `Player updated successfully`,
      status: 'success',
      data: player
    });
  } catch (error) {
    console.error('Error updating player:', error);
    res.status(500).json({
      message: 'Failed to update player',
      status: 'error',
      error: error.message
    });
  }
});

// DELETE /api/players/:id - Delete player
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const player = await Player.findByPk(id);
    
    if (!player) {
      return res.status(404).json({
        message: 'Player not found',
        status: 'error'
      });
    }

    await player.destroy();

    res.json({
      message: `Player deleted successfully`,
      status: 'success'
    });
  } catch (error) {
    console.error('Error deleting player:', error);
    res.status(500).json({
      message: 'Failed to delete player',
      status: 'error',
      error: error.message
    });
  }
});

module.exports = router; 