{"name": "uno-backend-node", "version": "1.0.0", "description": "Real-time multiplayer Uno card game backend built with Node.js, Express, and Socket.IO", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["uno", "multiplayer", "real-time", "socket.io", "express", "node.js", "game"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.0", "socket.io": "^4.7.4", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.7", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}