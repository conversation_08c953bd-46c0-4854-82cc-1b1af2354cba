const { Sequelize } = require('sequelize');

// Initialize Sequelize with PostgreSQL
const sequelize = new Sequelize({
  dialect: 'postgres',
  host: 'localhost',
  port: 5432,
  database: 'testnode',
  username: 'postgres',
  password: '2025',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
});

// Import models
const Player = require('./Player')(sequelize);
const Game = require('./Game')(sequelize);
const Card = require('./Card')(sequelize);
const GamePlayer = require('./GamePlayer')(sequelize);

// Define associations
Game.belongsTo(Player, { as: 'createdBy', foreignKey: 'createdById' });
Game.belongsTo(Player, { as: 'winner', foreignKey: 'winnerId' });

Game.hasMany(GamePlayer, { foreignKey: 'gameId' });
GamePlayer.belongsTo(Game, { foreignKey: 'gameId' });

Player.hasMany(GamePlayer, { foreignKey: 'playerId' });
GamePlayer.belongsTo(Player, { foreignKey: 'playerId' });

Game.hasMany(Card, { foreignKey: 'gameId' });
Card.belongsTo(Game, { foreignKey: 'gameId' });

Player.hasMany(Card, { foreignKey: 'ownerId' });
Card.belongsTo(Player, { foreignKey: 'ownerId' });

// Test database connection and sync tables
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ PostgreSQL database connection established successfully.');

    // Sync all models (create tables if they don't exist)
    await sequelize.sync({ force: false });
    console.log('✅ Database tables synchronized successfully.');
  } catch (error) {
    console.error('❌ Unable to connect to PostgreSQL database:', error);
    throw error;
  }
};

module.exports = {
  sequelize,
  Player,
  Game,
  Card,
  GamePlayer,
  testConnection
}; 