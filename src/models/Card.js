const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Card = sequelize.define('Card', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    cardType: {
      type: DataTypes.ENUM('NUMBER', 'SKIP', 'REVERSE', 'DRAW_TWO', 'WILD', 'WILD_DRAW_FOUR'),
      allowNull: false
    },
    color: {
      type: DataTypes.ENUM('RED', 'BLUE', 'GREEN', 'YELLOW', 'WILD'),
      allowNull: false
    },
    value: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0,
        max: 9
      }
    },
    positionInHand: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    gameId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    ownerId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    isTopCard: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    isInDeck: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    tableName: 'cards',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    indexes: [
      {
        fields: ['gameId', 'ownerId']
      },
      {
        fields: ['gameId', 'isTopCard']
      },
      {
        fields: ['gameId', 'isInDeck']
      }
    ]
  });

  return Card;
}; 