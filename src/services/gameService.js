const { Game, Player, GamePlayer, Card } = require('../models');
const {
  generateGameCode,
  createDeck,
  shuffleDeck,
  dealCards,
  canPlayCard,
  getPlayableCards,
  calculateCardScore,
  getNextPlayerIndex,
  isValidColor,
  canStartGame,
  hasPlayerWon,
  shouldCallUno,
  CARD_TYPES,
  CARD_COLORS
} = require('../utils/unoUtils');

// In-memory game state for active games
const activeGames = new Map();

// Create a new game
const createGame = async (playerId, maxPlayers = 4, minPlayers = 2) => {
  try {
    // Validate player exists
    const player = await Player.findByPk(playerId);
    if (!player) {
      throw new Error('Player not found');
    }

    // Generate unique game code
    let gameCode;
    let existingGame;
    do {
      gameCode = generateGameCode();
      existingGame = await Game.findOne({ where: { gameCode } });
    } while (existingGame);

    // Create game in database
    const game = await Game.create({
      gameCode,
      maxPlayers,
      minPlayers,
      createdById: playerId,
      status: 'WAITING_FOR_PLAYERS'
    });

    // Add creator as first player
    await GamePlayer.create({
      gameId: game.id,
      playerId: playerId,
      playerOrder: 0
    });

    // Initialize in-memory game state
    const gameState = {
      id: game.id,
      gameCode,
      status: 'WAITING_FOR_PLAYERS',
      maxPlayers,
      minPlayers,
      currentPlayerIndex: 0,
      direction: 'CLOCKWISE',
      players: [{
        id: playerId,
        playerName: player.playerName,
        playerOrder: 0,
        cardsCount: 0,
        isActive: true,
        hasCalledUno: false
      }],
      deck: [],
      topCard: null,
      createdBy: {
        id: player.id,
        playerName: player.playerName,
        coins: player.coins
      },
      createdAt: game.createdAt,
      startedAt: null,
      finishedAt: null,
      winner: null
    };

    activeGames.set(gameCode, gameState);

    return gameState;
  } catch (error) {
    console.error('Error creating game:', error);
    throw error;
  }
};

// Join an existing game
const joinGame = async (gameCode, playerId) => {
  try {
    // Validate player exists
    const player = await Player.findByPk(playerId);
    if (!player) {
      throw new Error('Player not found');
    }

    // Find game
    const game = await Game.findOne({ where: { gameCode } });
    if (!game) {
      throw new Error('Game not found');
    }

    if (game.status !== 'WAITING_FOR_PLAYERS') {
      throw new Error('Game is not accepting new players');
    }

    // Check if player is already in game
    const existingPlayer = await GamePlayer.findOne({
      where: { gameId: game.id, playerId }
    });
    if (existingPlayer) {
      throw new Error('Player is already in this game');
    }

    // Check if game is full
    const currentPlayers = await GamePlayer.count({ where: { gameId: game.id } });
    if (currentPlayers >= game.maxPlayers) {
      throw new Error('Game is full');
    }

    // Add player to game
    const playerOrder = currentPlayers;
    await GamePlayer.create({
      gameId: game.id,
      playerId: playerId,
      playerOrder
    });

    // Update in-memory game state
    const gameState = activeGames.get(gameCode);
    if (gameState) {
      gameState.players.push({
        id: playerId,
        playerName: player.playerName,
        playerOrder,
        cardsCount: 0,
        isActive: true,
        hasCalledUno: false
      });

      // Check if game can start card distribution
      if (canStartGame(gameState.players.length, gameState.minPlayers)) {
        // Start card distribution after 2 seconds
        setTimeout(async () => {
          await startCardDistribution(gameCode);
        }, 2000);
      }
    }

    return gameState;
  } catch (error) {
    console.error('Error joining game:', error);
    throw error;
  }
};

// Start card distribution
const startCardDistribution = async (gameCode) => {
  try {
    const gameState = activeGames.get(gameCode);
    if (!gameState) {
      throw new Error('Game not found');
    }

    if (gameState.status !== 'WAITING_FOR_PLAYERS') {
      throw new Error('Game is not accepting card distribution');
    }

    // Update game status to distributing cards
    gameState.status = 'DISTRIBUTING_CARDS';
    gameState.distributionComplete = new Set(); // Track which players completed distribution

    // Create and shuffle deck
    let deck = createDeck();
    deck = shuffleDeck(deck);

    // Deal cards to players
    const { hands, remainingDeck } = dealCards(deck, gameState.players.length);

    // Save cards to database and update player hands
    for (let i = 0; i < gameState.players.length; i++) {
      const player = gameState.players[i];
      const hand = hands[i];

      // Save cards to database
      for (let j = 0; j < hand.length; j++) {
        const card = hand[j];
        await Card.create({
          cardType: card.cardType,
          color: card.color,
          value: card.value,
          positionInHand: j,
          gameId: gameState.id,
          ownerId: player.id,
          isTopCard: false,
          isInDeck: false
        });
      }

      // Update player's card count
      player.cardsCount = hand.length;
      player.hand = hand;
    }

    // Set top card from remaining deck
    const topCard = remainingDeck.pop();
    await Card.create({
      cardType: topCard.cardType,
      color: topCard.color,
      value: topCard.value,
      gameId: gameState.id,
      isTopCard: true,
      isInDeck: false
    });
    gameState.topCard = topCard;

    // Save remaining deck
    for (let i = 0; i < remainingDeck.length; i++) {
      const card = remainingDeck[i];
      await Card.create({
        cardType: card.cardType,
        color: card.color,
        value: card.value,
        gameId: gameState.id,
        isTopCard: false,
        isInDeck: true
      });
    }

    // Update database
    await Game.update(
      { status: 'DISTRIBUTING_CARDS' },
      { where: { gameCode } }
    );

    console.log(`🃏 Starting card distribution for game ${gameCode}`);

    // Emit card distribution event to all players
    const io = require('../index').io;
    io.to(gameCode).emit('CARD_DISTRIBUTION_STARTED', {
      gameCode,
      players: gameState.players.map(p => ({
        id: p.id,
        playerName: p.playerName,
        cardsCount: p.cardsCount,
        hand: p.hand // Send full hand to each player
      })),
      topCard: gameState.topCard
    });

  } catch (error) {
    console.error('Error starting card distribution:', error);
    throw error;
  }
};

// Handle card distribution complete from player
const handleCardDistributionComplete = async (gameCode, playerId) => {
  try {
    const gameState = activeGames.get(gameCode);
    if (!gameState) {
      throw new Error('Game not found');
    }

    if (gameState.status !== 'DISTRIBUTING_CARDS') {
      throw new Error('Game is not in card distribution phase');
    }

    // Mark this player as having completed distribution
    gameState.distributionComplete.add(playerId);

    console.log(`🃏 Player ${playerId} completed card distribution for game ${gameCode}`);
    console.log(`🃏 Distribution complete: ${gameState.distributionComplete.size}/${gameState.players.length}`);

    // Check if all players have completed distribution
    if (gameState.distributionComplete.size === gameState.players.length) {
      // All players completed distribution, start the actual game
      await startActualGame(gameCode);
    }

  } catch (error) {
    console.error('Error handling card distribution complete:', error);
    throw error;
  }
};

// Start the actual game after card distribution
const startActualGame = async (gameCode) => {
  try {
    const gameState = activeGames.get(gameCode);
    if (!gameState) {
      throw new Error('Game not found');
    }

    if (gameState.status !== 'DISTRIBUTING_CARDS') {
      throw new Error('Game is not in card distribution phase');
    }

    // Cards are already distributed, just start the game
    gameState.status = 'IN_PROGRESS';
    gameState.currentPlayerIndex = 0; // First player starts
    gameState.direction = 'CLOCKWISE';
    gameState.startedAt = new Date();

    // Cards are already distributed and saved to database
    // Just update the game state

    // Update database
    await Game.update({
      status: 'IN_PROGRESS',
      startedAt: gameState.startedAt,
      currentPlayerIndex: gameState.currentPlayerIndex,
      direction: gameState.direction
    }, { where: { gameCode } });

    console.log(`🎮 Game ${gameCode} started! Current player: ${gameState.players[gameState.currentPlayerIndex].playerName}`);

    // Emit game start event to all players
    const io = require('../index').io;
    io.to(gameCode).emit('GAME_STARTED', {
      gameCode,
      status: 'IN_PROGRESS',
      currentPlayerIndex: gameState.currentPlayerIndex,
      currentPlayer: gameState.players[gameState.currentPlayerIndex],
      direction: gameState.direction,
      topCard: gameState.topCard,
      players: gameState.players.map(p => ({
        id: p.id,
        playerName: p.playerName,
        cardsCount: p.cardsCount,
        playerOrder: p.playerOrder,
        isActive: p.isActive,
        hasCalledUno: p.hasCalledUno
      }))
    });

    return gameState;
  } catch (error) {
    console.error('Error starting game:', error);
    throw error;
  }
};

// Play a card
const playCard = async (gameCode, playerId, cardId, chosenColor = null) => {
  try {
    const gameState = activeGames.get(gameCode);
    if (!gameState) {
      throw new Error('Game not found');
    }

    if (gameState.status !== 'IN_PROGRESS') {
      throw new Error('Game is not in progress');
    }

    // Check if it's player's turn
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    if (currentPlayer.id !== playerId) {
      throw new Error('Not your turn');
    }

    // Find the card in player's hand
    const player = gameState.players.find(p => p.id === playerId);
    const card = player.hand.find(c => c.id === cardId);
    if (!card) {
      throw new Error('Card not found in hand');
    }

    // Check if card can be played
    if (!canPlayCard(card, gameState.topCard)) {
      throw new Error('Cannot play this card');
    }

    // Validate color choice for wild cards
    if ((card.cardType === CARD_TYPES.WILD || card.cardType === CARD_TYPES.WILD_DRAW_FOUR) && !chosenColor) {
      throw new Error('Must choose a color for wild card');
    }

    if (chosenColor && !isValidColor(chosenColor)) {
      throw new Error('Invalid color choice');
    }

    // Remove card from player's hand
    player.hand = player.hand.filter(c => c.id !== cardId);
    player.cardsCount = player.hand.length;

    // Update card in database
    await Card.update({
      ownerId: null,
      isTopCard: true,
      positionInHand: null
    }, { where: { id: cardId } });

    // Remove top card status from previous top card
    if (gameState.topCard) {
      await Card.update({
        isTopCard: false
      }, { where: { id: gameState.topCard.id } });
    }

    // Set new top card
    const newTopCard = { ...card };
    if (chosenColor && (card.cardType === CARD_TYPES.WILD || card.cardType === CARD_TYPES.WILD_DRAW_FOUR)) {
      newTopCard.color = chosenColor;
    }
    gameState.topCard = newTopCard;

    // Handle special card effects
    let nextPlayerIndex = gameState.currentPlayerIndex;
    let skipNextPlayer = false;
    let drawCards = 0;

    switch (card.cardType) {
      case CARD_TYPES.SKIP:
        skipNextPlayer = true;
        break;
      case CARD_TYPES.REVERSE:
        gameState.direction = gameState.direction === 'CLOCKWISE' ? 'COUNTERCLOCKWISE' : 'CLOCKWISE';
        break;
      case CARD_TYPES.DRAW_TWO:
        drawCards = 2;
        skipNextPlayer = true;
        break;
      case CARD_TYPES.WILD_DRAW_FOUR:
        drawCards = 4;
        skipNextPlayer = true;
        break;
    }

    // Move to next player
    if (skipNextPlayer) {
      nextPlayerIndex = getNextPlayerIndex(nextPlayerIndex, gameState.direction, gameState.players.length);
    }
    nextPlayerIndex = getNextPlayerIndex(nextPlayerIndex, gameState.direction, gameState.players.length);

    // Handle draw cards effect
    if (drawCards > 0) {
      const nextPlayer = gameState.players[nextPlayerIndex];
      // Draw cards for next player (simplified - would need to implement draw from deck)
      nextPlayer.cardsCount += drawCards;
    }

    // Update current player index
    gameState.currentPlayerIndex = nextPlayerIndex;

    // Check for win condition
    if (hasPlayerWon(player.hand)) {
      gameState.status = 'FINISHED';
      gameState.winner = {
        id: player.id,
        playerName: player.playerName
      };
      gameState.finishedAt = new Date();

      // Update database
      await Game.update({
        status: 'FINISHED',
        winnerId: player.id,
        finishedAt: gameState.finishedAt
      }, { where: { gameCode } });
    }

    return gameState;
  } catch (error) {
    console.error('Error playing card:', error);
    throw error;
  }
};

// Draw a card
const drawCard = async (gameCode, playerId) => {
  try {
    const gameState = activeGames.get(gameCode);
    if (!gameState) {
      throw new Error('Game not found');
    }

    if (gameState.status !== 'IN_PROGRESS') {
      throw new Error('Game is not in progress');
    }

    // Check if it's player's turn
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    if (currentPlayer.id !== playerId) {
      throw new Error('Not your turn');
    }

    // Check if player has playable cards
    const player = gameState.players.find(p => p.id === playerId);
    const playableCards = getPlayableCards(player.hand, gameState.topCard);
    if (playableCards.length > 0) {
      throw new Error('You have playable cards');
    }

    // Draw a card from deck
    if (gameState.deck.length === 0) {
      throw new Error('No cards left in deck');
    }

    const drawnCard = gameState.deck.pop();
    const cardRecord = await Card.create({
      ...drawnCard,
      gameId: gameState.id,
      ownerId: playerId,
      positionInHand: player.hand.length,
      isInDeck: false
    });

    player.hand.push(cardRecord);
    player.cardsCount = player.hand.length;

    // Move to next player
    gameState.currentPlayerIndex = getNextPlayerIndex(
      gameState.currentPlayerIndex,
      gameState.direction,
      gameState.players.length
    );

    return gameState;
  } catch (error) {
    console.error('Error drawing card:', error);
    throw error;
  }
};

// Call Uno
const callUno = async (gameCode, playerId) => {
  try {
    const gameState = activeGames.get(gameCode);
    if (!gameState) {
      throw new Error('Game not found');
    }

    const player = gameState.players.find(p => p.id === playerId);
    if (!player) {
      throw new Error('Player not found in game');
    }

    if (player.cardsCount !== 1) {
      throw new Error('Can only call Uno when you have 1 card left');
    }

    player.hasCalledUno = true;

    return gameState;
  } catch (error) {
    console.error('Error calling Uno:', error);
    throw error;
  }
};

// Get game state
const getGameState = async (gameCode, playerId = null) => {
  try {
    const gameState = activeGames.get(gameCode);
    if (!gameState) {
      throw new Error('Game not found');
    }

    // If playerId is provided, include their hand
    if (playerId) {
      const player = gameState.players.find(p => p.id === playerId);
      if (player && player.hand) {
        return {
          ...gameState,
          currentPlayer: {
            id: player.id,
            playerName: player.playerName,
            hand: player.hand
          }
        };
      }
    }

    return gameState;
  } catch (error) {
    console.error('Error getting game state:', error);
    throw error;
  }
};

// Get all available games
const getAvailableGames = async () => {
  try {
    const games = await Game.findAll({
      where: { status: 'WAITING_FOR_PLAYERS' },
      include: [
        { model: Player, as: 'createdBy', attributes: ['id', 'playerName'] },
        { model: GamePlayer, include: [{ model: Player, attributes: ['id', 'playerName'] }] }
      ],
      order: [['createdAt', 'DESC']]
    });

    return games.map(game => ({
      id: game.id,
      gameCode: game.gameCode,
      status: game.status,
      maxPlayers: game.maxPlayers,
      minPlayers: game.minPlayers,
      currentPlayers: game.GamePlayers.length,
      createdBy: game.createdBy,
      createdAt: game.createdAt
    }));
  } catch (error) {
    console.error('Error getting available games:', error);
    throw error;
  }
};

// Get player's active games
const getPlayerGames = async (playerId) => {
  try {
    const games = await Game.findAll({
      include: [
        {
          model: GamePlayer,
          where: { playerId },
          include: [{ model: Player, attributes: ['id', 'playerName'] }]
        },
        { model: Player, as: 'createdBy', attributes: ['id', 'playerName'] }
      ],
      order: [['createdAt', 'DESC']]
    });

    return games.map(game => ({
      id: game.id,
      gameCode: game.gameCode,
      status: game.status,
      maxPlayers: game.maxPlayers,
      minPlayers: game.minPlayers,
      currentPlayers: game.GamePlayers.length,
      createdBy: game.createdBy,
      createdAt: game.createdAt,
      startedAt: game.startedAt,
      finishedAt: game.finishedAt
    }));
  } catch (error) {
    console.error('Error getting player games:', error);
    throw error;
  }
};

module.exports = {
  createGame,
  joinGame,
  startGame: startActualGame, // Keep the old name for compatibility
  startCardDistribution,
  handleCardDistributionComplete,
  playCard,
  drawCard,
  callUno,
  getGameState,
  getAvailableGames,
  getPlayerGames,
  activeGames
};