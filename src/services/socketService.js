const gameService = require('./gameService');

// Optimized event data structures
const createOptimizedEventData = {
  playerJoined: (playerName, playerOrder, totalPlayers) => ({
    playerName,
    playerOrder,
    totalPlayers
  }),

  gameStarted: (totalPlayers, currentPlayerId, currentPlayerName, topCard) => ({
    totalPlayers,
    currentPlayerId,
    currentPlayerName,
    topCard
  }),

  gameUpdate: (totalPlayers, currentPlayerId, currentPlayerName, topCard, gameStatus, direction) => ({
    totalPlayers,
    currentPlayerId,
    currentPlayerName,
    topCard,
    gameStatus,
    direction
  }),

  cardPlayed: (cardId, newTopCard, nextPlayerId, nextPlayerName, cardsRemaining, direction) => ({
    cardId,
    newTopCard,
    nextPlayerId,
    nextPlayerName,
    cardsRemaining,
    direction
  }),

  cardDrawn: (cardsDrawn, totalCardsInHand, nextPlayerId, nextPlayerName, deckSize) => ({
    cardsDrawn,
    totalCardsInHand,
    nextPlayerId,
    nextPlayerName,
    deckSize
  }),

  unoCalled: (playerName, cardsRemaining) => ({
    playerName,
    cardsRemaining
  }),

  gameWon: (winnerName, finalScore) => ({
    winnerName,
    finalScore
  }),

  turnSkipped: (skippedPlayerId, skippedPlayerName, nextPlayerId, nextPlayerName) => ({
    skippedPlayerId,
    skippedPlayerName,
    nextPlayerId,
    nextPlayerName
  }),

  directionChanged: (newDirection, nextPlayerId, nextPlayerName) => ({
    newDirection,
    nextPlayerId,
    nextPlayerName
  }),

  colorChanged: (newColor, nextPlayerId, nextPlayerName) => ({
    newColor,
    nextPlayerId,
    nextPlayerName
  })
};

// Create optimized game event
const createOptimizedEvent = (eventType, gameCode, playerId, playerName, eventData) => ({
  eventType,
  gameCode,
  playerId,
  playerName,
  timestamp: new Date().toISOString(),
  eventData
});

// Broadcast optimized event to game room
const broadcastOptimizedEvent = (io, gameCode, event) => {
  console.log(`🔔 Broadcasting optimized WebSocket message:
   Topic: /game/${gameCode}
   Event Type: ${event.eventType}
   Player: ${event.playerName || 'null'} (ID: ${event.playerId || 'null'})
   Data Size: ~${JSON.stringify(event).length} bytes`);

  io.to(`game_${gameCode}`).emit(event.eventType, event);
  console.log('✅ Optimized WebSocket message sent successfully');
};

// Setup Socket.IO event handlers
const setupSocket = (io) => {
  io.on('connection', (socket) => {
    console.log(`🔌 New client connected: ${socket.id}`);

    // Join game room
    socket.on('joinGame', async (data) => {
      try {
        const { gameCode, playerId } = data;

        if (!gameCode || !playerId) {
          socket.emit('error', { message: 'Game code and player ID are required' });
          return;
        }

        // Join the game room
        socket.join(`game_${gameCode}`);
        socket.gameCode = gameCode;
        socket.playerId = playerId;

        console.log(`📡 Player ${playerId} joined game room: ${gameCode}`);

        // Get current game state
        const gameState = gameService.activeGames.get(gameCode);
        if (gameState) {
          // Send current game state to the joining player
          socket.emit('GAME_STATE', gameState);
        }

        socket.emit('joinedGame', { gameCode, playerId });
      } catch (error) {
        console.error('Error joining game:', error);
        socket.emit('error', { message: error.message });
      }
    });

    // Play card
    socket.on('playCard', async (data) => {
      try {
        const { gameCode, playerId, cardId, chosenColor } = data;

        if (!gameCode || !playerId || !cardId) {
          socket.emit('error', { message: 'Game code, player ID, and card ID are required' });
          return;
        }

        const gameState = await gameService.playCard(gameCode, playerId, cardId, chosenColor);

        if (gameState) {
          const currentPlayer = gameState.players[gameState.currentPlayerIndex];
          const nextPlayer = gameState.players[gameState.currentPlayerIndex];

          // Create optimized event
          const eventData = createOptimizedEventData.cardPlayed(
            cardId,
            gameState.topCard,
            nextPlayer?.id,
            nextPlayer?.playerName,
            gameState.players.find(p => p.id === playerId)?.cardsCount || 0,
            gameState.direction
          );

          const event = createOptimizedEvent('CARD_PLAYED', gameCode, playerId,
            gameState.players.find(p => p.id === playerId)?.playerName, eventData);

          // Broadcast to all players in the game
          broadcastOptimizedEvent(io, gameCode, event);

          // Check for game end
          if (gameState.status === 'FINISHED') {
            const winnerEvent = createOptimizedEvent('GAME_WON', gameCode,
              gameState.winner?.id, gameState.winner?.playerName,
              createOptimizedEventData.gameWon(gameState.winner?.playerName, 0));

            broadcastOptimizedEvent(io, gameCode, winnerEvent);
          }
        }
      } catch (error) {
        console.error('Error playing card:', error);
        socket.emit('error', { message: error.message });
      }
    });

    // Draw card
    socket.on('drawCard', async (data) => {
      try {
        const { gameCode, playerId } = data;

        if (!gameCode || !playerId) {
          socket.emit('error', { message: 'Game code and player ID are required' });
          return;
        }

        const gameState = await gameService.drawCard(gameCode, playerId);

        if (gameState) {
          const nextPlayer = gameState.players[gameState.currentPlayerIndex];
          const currentPlayer = gameState.players.find(p => p.id === playerId);

          const eventData = createOptimizedEventData.cardDrawn(
            1, // cards drawn
            currentPlayer?.cardsCount || 0,
            nextPlayer?.id,
            nextPlayer?.playerName,
            gameState.deck.length
          );

          const event = createOptimizedEvent('CARD_DRAWN', gameCode, playerId,
            currentPlayer?.playerName, eventData);

          broadcastOptimizedEvent(io, gameCode, event);
        }
      } catch (error) {
        console.error('Error drawing card:', error);
        socket.emit('error', { message: error.message });
      }
    });

    // Call Uno
    socket.on('callUno', async (data) => {
      try {
        const { gameCode, playerId } = data;

        if (!gameCode || !playerId) {
          socket.emit('error', { message: 'Game code and player ID are required' });
          return;
        }

        const gameState = await gameService.callUno(gameCode, playerId);

        if (gameState) {
          const player = gameState.players.find(p => p.id === playerId);

          const eventData = createOptimizedEventData.unoCalled(
            player?.playerName,
            player?.cardsCount || 0
          );

          const event = createOptimizedEvent('UNO_CALLED', gameCode, playerId,
            player?.playerName, eventData);

          broadcastOptimizedEvent(io, gameCode, event);
        }
      } catch (error) {
        console.error('Error calling Uno:', error);
        socket.emit('error', { message: error.message });
      }
    });

    // Card distribution complete
    socket.on('cardDistributionComplete', async (data) => {
      try {
        const { gameCode, playerId } = data;

        if (!gameCode || !playerId) {
          socket.emit('error', { message: 'Game code and player ID are required' });
          return;
        }

        await gameService.handleCardDistributionComplete(gameCode, playerId);

        console.log(`🃏 Player ${playerId} completed card distribution for game ${gameCode}`);

      } catch (error) {
        console.error('Error handling card distribution complete:', error);
        socket.emit('error', { message: error.message });
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`🔌 Client disconnected: ${socket.id}`);
      // Could implement player leaving game logic here
    });
  });

  // Broadcast game events from game service
  const broadcastGameEvents = {
    playerJoined: (gameCode, playerId, playerName, playerOrder, totalPlayers) => {
      const eventData = createOptimizedEventData.playerJoined(playerName, playerOrder, totalPlayers);
      const event = createOptimizedEvent('PLAYER_JOINED', gameCode, playerId, playerName, eventData);
      broadcastOptimizedEvent(io, gameCode, event);
    },

    gameStarted: (gameCode, totalPlayers, currentPlayerId, currentPlayerName, topCard) => {
      const eventData = createOptimizedEventData.gameStarted(totalPlayers, currentPlayerId, currentPlayerName, topCard);
      const event = createOptimizedEvent('GAME_STARTED', gameCode, null, null, eventData);
      broadcastOptimizedEvent(io, gameCode, event);
    },

    gameUpdate: (gameCode, totalPlayers, currentPlayerId, currentPlayerName, topCard, gameStatus, direction) => {
      const eventData = createOptimizedEventData.gameUpdate(totalPlayers, currentPlayerId, currentPlayerName, topCard, gameStatus, direction);
      const event = createOptimizedEvent('GAME_UPDATE', gameCode, null, null, eventData);
      broadcastOptimizedEvent(io, gameCode, event);
    }
  };

  // Make broadcast functions available to game service
  gameService.broadcastEvents = broadcastGameEvents;
};

module.exports = { setupSocket }; 