const gameService = require('../services/gameService');

// Create a new game
exports.createGame = async (req, res) => {
  try {
    const { playerId, maxPlayers = 4, minPlayers = 2 } = req.body;

    if (!playerId) {
      return res.status(400).json({
        error: 'Player ID is required'
      });
    }

    if (maxPlayers < minPlayers) {
      return res.status(400).json({
        error: 'Max players must be greater than or equal to min players'
      });
    }

    const gameState = await gameService.createGame(playerId, maxPlayers, minPlayers);

    res.status(201).json(gameState);
  } catch (error) {
    console.error('Error creating game:', error);
    res.status(500).json({
      error: error.message || 'Internal server error'
    });
  }
};

// Join an existing game
exports.joinGame = async (req, res) => {
  try {
    const { gameCode, playerId } = req.body;

    if (!gameCode || !playerId) {
      return res.status(400).json({
        error: 'Game code and player ID are required'
      });
    }

    const gameState = await gameService.joinGame(gameCode, playerId);

    res.json(gameState);
  } catch (error) {
    console.error('Error joining game:', error);
    res.status(500).json({
      error: error.message || 'Internal server error'
    });
  }
};

// Get game details
exports.getGameDetails = async (req, res) => {
  try {
    const { gameCode } = req.params;
    const { playerId } = req.query;

    if (!gameCode) {
      return res.status(400).json({
        error: 'Game code is required'
      });
    }

    const gameState = await gameService.getGameState(gameCode, playerId);

    res.json(gameState);
  } catch (error) {
    console.error('Error getting game details:', error);
    res.status(500).json({
      error: error.message || 'Internal server error'
    });
  }
};

// Get all available games
exports.getAvailableGames = async (req, res) => {
  try {
    const games = await gameService.getAvailableGames();

    res.json(games);
  } catch (error) {
    console.error('Error getting available games:', error);
    res.status(500).json({
      error: error.message || 'Internal server error'
    });
  }
};

// Get player's active games
exports.getPlayerGames = async (req, res) => {
  try {
    const { playerId } = req.params;

    if (!playerId) {
      return res.status(400).json({
        error: 'Player ID is required'
      });
    }

    const games = await gameService.getPlayerGames(parseInt(playerId));

    res.json(games);
  } catch (error) {
    console.error('Error getting player games:', error);
    res.status(500).json({
      error: error.message || 'Internal server error'
    });
  }
};

// Play a card (WebSocket action, but also available via REST for testing)
exports.playCard = async (req, res) => {
  try {
    const { gameCode, playerId, cardId, chosenColor } = req.body;

    if (!gameCode || !playerId || !cardId) {
      return res.status(400).json({
        error: 'Game code, player ID, and card ID are required'
      });
    }

    const gameState = await gameService.playCard(gameCode, playerId, cardId, chosenColor);

    res.json(gameState);
  } catch (error) {
    console.error('Error playing card:', error);
    res.status(500).json({
      error: error.message || 'Internal server error'
    });
  }
};

// Draw a card (WebSocket action, but also available via REST for testing)
exports.drawCard = async (req, res) => {
  try {
    const { gameCode, playerId } = req.body;

    if (!gameCode || !playerId) {
      return res.status(400).json({
        error: 'Game code and player ID are required'
      });
    }

    const gameState = await gameService.drawCard(gameCode, playerId);

    res.json(gameState);
  } catch (error) {
    console.error('Error drawing card:', error);
    res.status(500).json({
      error: error.message || 'Internal server error'
    });
  }
};

// Call Uno (WebSocket action, but also available via REST for testing)
exports.callUno = async (req, res) => {
  try {
    const { gameCode, playerId } = req.body;

    if (!gameCode || !playerId) {
      return res.status(400).json({
        error: 'Game code and player ID are required'
      });
    }

    const gameState = await gameService.callUno(gameCode, playerId);

    res.json(gameState);
  } catch (error) {
    console.error('Error calling Uno:', error);
    res.status(500).json({
      error: error.message || 'Internal server error'
    });
  }
}; 