!function(a,b){if("function"==typeof define&&define.amd)define("Toposort",["exports","module"],b);else if("undefined"!=typeof exports&&"undefined"!=typeof module)b(exports,module);else{var c={exports:{}};b(c.exports,c),a.Toposort=c.exports}}(this,function(a,b){"use strict";function c(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}var d=function(){function a(){c(this,a),this.edges=[],this.Toposort=a}return a.prototype.add=function(a,b){if("string"!=typeof a||!a)throw new TypeError("Dependent name must be given as a not empty string");if(b=Array.isArray(b)?b:[b],b.length>0)for(var c=b,d=Array.isArray(c),e=0,c=d?c:c[Symbol.iterator]();;){var f;if(d){if(e>=c.length)break;f=c[e++]}else{if(e=c.next(),e.done)break;f=e.value}var g=f;if("string"!=typeof g||!g)throw new TypeError("Dependency name must be given as a not empty string");this.edges.push([a,g])}else this.edges.push([a]);return this},a.prototype.sort=function(){for(var a=this,b=[],c=this.edges,d=Array.isArray(c),e=0,c=d?c:c[Symbol.iterator]();;){var f;if(d){if(e>=c.length)break;f=c[e++]}else{if(e=c.next(),e.done)break;f=e.value}for(var g=f,h=g,i=Array.isArray(h),j=0,h=i?h:h[Symbol.iterator]();;){var k;if(i){if(j>=h.length)break;k=h[j++]}else{if(j=h.next(),j.done)break;k=j.value}var l=k;-1===b.indexOf(l)&&b.push(l)}}for(var m=b.length,n=new Array(b.length),o=function u(c,d){if(0!==d.length&&-1!==d.indexOf(c))throw new Error("Cyclic dependency found. "+c+" is dependent of itself.\nDependency chain: "+d.join(" -> ")+" => "+c);var e=b.indexOf(c);if(-1!==e){var f=!1;b[e]=!1;for(var g=a.edges,h=Array.isArray(g),i=0,g=h?g:g[Symbol.iterator]();;){var j;if(h){if(i>=g.length)break;j=g[i++]}else{if(i=g.next(),i.done)break;j=i.value}var k=j;k[0]===c&&(f=f||d.concat([c]),u(k[1],f))}n[--m]=c}},p=0;p<b.length;p++){var l=b[p];if(l!==!1){b[p]=!1;for(var q=this.edges,r=Array.isArray(q),s=0,q=r?q:q[Symbol.iterator]();;){var t;if(r){if(s>=q.length)break;t=q[s++]}else{if(s=q.next(),s.done)break;t=s.value}var g=t;g[0]===l&&o(g[1],[l])}n[--m]=l}}return n},a.prototype.clear=function(){return this.edges=[],this},a}();b.exports=d});