# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [3.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@3.0.0) (2019-12-20)

**Note:** Version bump only for package istanbul-lib-coverage





# [3.0.0-alpha.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@3.0.0-alpha.2) (2019-12-07)

**Note:** Version bump only for package istanbul-lib-coverage





# [3.0.0-alpha.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@3.0.0-alpha.1) (2019-10-06)


### Bug Fixes

* Drop unneeded coverage data from `nyc --all` ([#456](https://github.com/istanbuljs/istanbuljs/issues/456)) ([f6bb0b4](https://github.com/istanbuljs/istanbuljs/commit/f6bb0b4)), closes [#123](https://github.com/istanbuljs/istanbuljs/issues/123) [#224](https://github.com/istanbuljs/istanbuljs/issues/224) [#260](https://github.com/istanbuljs/istanbuljs/issues/260) [#322](https://github.com/istanbuljs/istanbuljs/issues/322) [#413](https://github.com/istanbuljs/istanbuljs/issues/413)





# [3.0.0-alpha.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@3.0.0-alpha.0) (2019-06-19)


### Features

* Update dependencies, require Node.js 8 ([#401](https://github.com/istanbuljs/istanbuljs/issues/401)) ([bf3a539](https://github.com/istanbuljs/istanbuljs/commit/bf3a539))


### BREAKING CHANGES

* Node.js 8 is now required





## [3.2.2](https://github.com/istanbuljs/istanbuljs/compare/istanbul-lib-coverage-v3.2.1...istanbul-lib-coverage-v3.2.2) (2023-11-07)


### Bug Fixes

* [#753](https://github.com/istanbuljs/istanbuljs/issues/753) ([c4895bb](https://github.com/istanbuljs/istanbuljs/commit/c4895bb418c55700182f481b914b74b2865a9bea))
* Proper data validation in findNearestContainer ([#754](https://github.com/istanbuljs/istanbuljs/issues/754)) ([c4895bb](https://github.com/istanbuljs/istanbuljs/commit/c4895bb418c55700182f481b914b74b2865a9bea)), closes [#753](https://github.com/istanbuljs/istanbuljs/issues/753)

## [3.2.1](https://github.com/istanbuljs/istanbuljs/compare/istanbul-lib-coverage-v3.2.0...istanbul-lib-coverage-v3.2.1) (2023-11-04)


### Bug Fixes

* https://github.com/istanbuljs/v8-to-istanbul/issues/233 ([288888f](https://github.com/istanbuljs/istanbuljs/commit/288888fef02ea35baf536bdd4d390ee12233ceb3))
* merge ranges properly when contained by other ranges in set ([#750](https://github.com/istanbuljs/istanbuljs/issues/750)) ([288888f](https://github.com/istanbuljs/istanbuljs/commit/288888fef02ea35baf536bdd4d390ee12233ceb3))

## [3.2.0](https://www.github.com/istanbuljs/istanbuljs/compare/istanbul-lib-coverage-v3.1.0...istanbul-lib-coverage-v3.2.0) (2021-10-17)


### Features

* allow FileCoverage to be initialized with logical tracking ([#644](https://www.github.com/istanbuljs/istanbuljs/issues/644)) ([4cb5af1](https://www.github.com/istanbuljs/istanbuljs/commit/4cb5af1daaf33c3e9a5f3ee44f6bb7f958e5ba04))

## [3.1.0](https://www.github.com/istanbuljs/istanbuljs/compare/istanbul-lib-coverage-v3.0.2...istanbul-lib-coverage-v3.1.0) (2021-10-17)


### Features

* support tracking Logic Truthiness as additional metric in coverage API ([#639](https://www.github.com/istanbuljs/istanbuljs/issues/639)) ([0967c80](https://www.github.com/istanbuljs/istanbuljs/commit/0967c80b905c3c17675ff2185b2325784e8dc0a2))

### [3.0.2](https://www.github.com/istanbuljs/istanbuljs/compare/istanbul-lib-coverage-v3.0.1...istanbul-lib-coverage-v3.0.2) (2021-10-11)


### Bug Fixes

* handle merging '0' indexed coverage with '1' indexed coverage ([5dac2bc](https://www.github.com/istanbuljs/istanbuljs/commit/5dac2bcf28d6f27dbb720be72c2b692153418ab5)), closes [#632](https://www.github.com/istanbuljs/istanbuljs/issues/632)

### [3.0.1](https://www.github.com/istanbuljs/istanbuljs/compare/istanbul-lib-coverage-v3.0.0...istanbul-lib-coverage-v3.0.1) (2021-09-23)


### Bug Fixes

* merge branch/statement/functionMap's together when merging two coverage reports ([#617](https://www.github.com/istanbuljs/istanbuljs/issues/617)) ([ff1b5e9](https://www.github.com/istanbuljs/istanbuljs/commit/ff1b5e915201e4ff8f737010509bab98d8238118))

## [2.0.5](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@2.0.5) (2019-04-24)

**Note:** Version bump only for package istanbul-lib-coverage





## [2.0.4](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@2.0.4) (2019-03-12)

**Note:** Version bump only for package istanbul-lib-coverage





## [2.0.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@2.0.3) (2019-01-26)

**Note:** Version bump only for package istanbul-lib-coverage





<a name="2.0.2"></a>
## [2.0.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@2.0.2) (2018-12-25)




**Note:** Version bump only for package istanbul-lib-coverage

<a name="2.0.1"></a>
## [2.0.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@2.0.1) (2018-07-07)




**Note:** Version bump only for package istanbul-lib-coverage

<a name="2.0.0"></a>
# [2.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@2.0.0) (2018-06-06)


### Bug Fixes

* use null prototype for map objects ([#177](https://github.com/istanbuljs/istanbuljs/issues/177)) ([9a5a30c](https://github.com/istanbuljs/istanbuljs/commit/9a5a30c))


### BREAKING CHANGES

* a null prototype is now used in several places rather than the default `{}` assignment.




<a name="1.2.0"></a>
# [1.2.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@1.2.0) (2018-03-04)


### Features

* add skip-empty option for html & text reports ([#140](https://github.com/istanbuljs/istanbuljs/issues/140)) ([d2a4262](https://github.com/istanbuljs/istanbuljs/commit/d2a4262))




<a name="1.1.2"></a>
## [1.1.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@1.1.2) (2018-02-13)




**Note:** Version bump only for package istanbul-lib-coverage

<a name="1.1.1"></a>
## [1.1.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-coverage@1.1.1) (2017-05-27)




<a name="1.1.0"></a>
# [1.1.0](https://github.com/istanbuljs/istanbul-lib-coverage/compare/<EMAIL>-lib-coverage@1.1.0) (2017-04-29)


### Bug Fixes

* getBranchCoverageByLine() was looking for line coverage using wrong object structure ([bf36658](https://github.com/istanbuljs/istanbul-lib-coverage/commit/bf36658))


### Features

* add possibility to filter coverage maps when running reports post-hoc ([#24](https://github.com/istanbuljs/istanbuljs/issues/24)) ([e1c99d6](https://github.com/istanbuljs/istanbul-lib-coverage/commit/e1c99d6))




<a name="1.0.2"></a>
## [1.0.2](https://github.com/istanbuljs/istanbul-lib-coverage/compare/<EMAIL>-lib-coverage@1.0.2) (2017-03-27)

<a name="1.0.1"></a>
## [1.0.1](https://github.com/istanbuljs/istanbul-lib-coverage/compare/v1.0.0...v1.0.1) (2017-01-18)


### Bug Fixes

* handle edge-case surrounding merging two file coverage reports ([22e154c](https://github.com/istanbuljs/istanbul-lib-coverage/commit/22e154c))



<a name="1.0.0"></a>
# [1.0.0](https://github.com/istanbuljs/istanbul-lib-coverage/compare/v1.0.0-alpha.3...v1.0.0) (2016-08-12)


### Bug Fixes

* guard against missing statement ([76aad99](https://github.com/istanbuljs/istanbul-lib-coverage/commit/76aad99))
