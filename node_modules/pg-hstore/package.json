{"author": "<PERSON> <<EMAIL>> (https://github.com/scarney81)", "name": "pg-hstore", "description": "A module for serializing and deserializing JSON data into hstore format", "private": false, "keywords": ["pg", "postgres", "hstore"], "license": "MIT", "version": "2.3.4", "main": "lib/index.js", "homepage": "https://github.com/scarney81/pg-hstore", "bugs": {"url": "https://github.com/scarney81/pg-hstore/issues"}, "repository": {"type": "git", "url": "**************:scarney81/pg-hstore.git"}, "dependencies": {"underscore": "^1.13.1"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.4.2"}, "engines": {"node": ">= 0.8.x"}, "scripts": {"test": "./node_modules/.bin/mocha --reporter spec -u bdd --require should --recursive --timeout 10000"}}