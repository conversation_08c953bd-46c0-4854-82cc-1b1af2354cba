{"name": "inflection", "version": "1.13.4", "description": "A port of inflection-js to node.js module", "keywords": ["inflection", "inflections", "inflection-js", "pluralize", "singularize", "camelize", "underscore", "humanize", "capitalize", "dasherize", "titleize", "demodulize", "tableize", "classify", "foreign_key", "ordinalize"], "author": "dreamerslab <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "lance<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "luk3thomas"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "overlookmotel"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "devDependencies": {"terser": "^5.15.0", "vitest": "^0.23.4"}, "main": "./lib/inflection.js", "repository": {"type": "git", "url": "https://github.com/dreamerslab/node.inflection.git"}, "engines": ["node >= 0.4.0"], "license": "MIT", "scripts": {"test": "vitest", "minify": "terser lib/inflection.js -o inflection.min.js -c -m"}}