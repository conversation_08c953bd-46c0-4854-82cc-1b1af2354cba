{"name": "collect-v8-coverage", "version": "1.0.2", "main": "index.js", "types": "index.d.ts", "repository": "SimenB/collect-v8-coverage", "files": ["CHANGELOG.md", "index.js", "index.d.ts"], "license": "MIT", "devDependencies": {"@commitlint/cli": "^17.0.0", "@commitlint/config-conventional": "^17.0.0", "@semantic-release/changelog": "^6.0.0", "@semantic-release/git": "^10.0.0", "husky": "^8.0.0", "lint-staged": "^13.0.0", "prettier": "^2.2.1", "semantic-release": "^21.0.0"}, "prettier": {"singleQuote": true, "trailingComma": "all"}, "lint-staged": {"*.{js,ts,md,json}": "prettier --write"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"]}, "scripts": {"prepare": "husky install"}, "packageManager": "yarn@3.6.0"}