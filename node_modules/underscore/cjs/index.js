Object.defineProperty(exports, '__esModule', { value: true });

var _setup = require('./_setup.js');
var restArguments = require('./restArguments.js');
var isObject = require('./isObject.js');
var isNull = require('./isNull.js');
var isUndefined = require('./isUndefined.js');
var isBoolean = require('./isBoolean.js');
var isElement = require('./isElement.js');
var isString = require('./isString.js');
var isNumber = require('./isNumber.js');
var isDate = require('./isDate.js');
var isRegExp = require('./isRegExp.js');
var isError = require('./isError.js');
var isSymbol = require('./isSymbol.js');
var isArrayBuffer = require('./isArrayBuffer.js');
var isDataView = require('./isDataView.js');
var isArray = require('./isArray.js');
var isFunction = require('./isFunction.js');
var isArguments = require('./isArguments.js');
var _isFinite = require('./isFinite.js');
var _isNaN = require('./isNaN.js');
var isTypedArray = require('./isTypedArray.js');
var isEmpty = require('./isEmpty.js');
var isMatch = require('./isMatch.js');
var isEqual = require('./isEqual.js');
var isMap = require('./isMap.js');
var isWeakMap = require('./isWeakMap.js');
var isSet = require('./isSet.js');
var isWeakSet = require('./isWeakSet.js');
var keys = require('./keys.js');
var allKeys = require('./allKeys.js');
var values = require('./values.js');
var pairs = require('./pairs.js');
var invert = require('./invert.js');
var functions = require('./functions.js');
var extend = require('./extend.js');
var extendOwn = require('./extendOwn.js');
var defaults = require('./defaults.js');
var create = require('./create.js');
var clone = require('./clone.js');
var tap = require('./tap.js');
var get = require('./get.js');
var has = require('./has.js');
var mapObject = require('./mapObject.js');
var identity = require('./identity.js');
var constant = require('./constant.js');
var noop = require('./noop.js');
var toPath = require('./toPath.js');
var property = require('./property.js');
var propertyOf = require('./propertyOf.js');
var matcher = require('./matcher.js');
var times = require('./times.js');
var random = require('./random.js');
var now = require('./now.js');
var _escape = require('./escape.js');
var _unescape = require('./unescape.js');
var templateSettings = require('./templateSettings.js');
var template = require('./template.js');
var result = require('./result.js');
var uniqueId = require('./uniqueId.js');
var chain = require('./chain.js');
var iteratee = require('./iteratee.js');
var partial = require('./partial.js');
var bind = require('./bind.js');
var bindAll = require('./bindAll.js');
var memoize = require('./memoize.js');
var delay = require('./delay.js');
var defer = require('./defer.js');
var throttle = require('./throttle.js');
var debounce = require('./debounce.js');
var wrap = require('./wrap.js');
var negate = require('./negate.js');
var compose = require('./compose.js');
var after = require('./after.js');
var before = require('./before.js');
var once = require('./once.js');
var findKey = require('./findKey.js');
var findIndex = require('./findIndex.js');
var findLastIndex = require('./findLastIndex.js');
var sortedIndex = require('./sortedIndex.js');
var indexOf = require('./indexOf.js');
var lastIndexOf = require('./lastIndexOf.js');
var find = require('./find.js');
var findWhere = require('./findWhere.js');
var each = require('./each.js');
var map = require('./map.js');
var reduce = require('./reduce.js');
var reduceRight = require('./reduceRight.js');
var filter = require('./filter.js');
var reject = require('./reject.js');
var every = require('./every.js');
var some = require('./some.js');
var contains = require('./contains.js');
var invoke = require('./invoke.js');
var pluck = require('./pluck.js');
var where = require('./where.js');
var max = require('./max.js');
var min = require('./min.js');
var shuffle = require('./shuffle.js');
var sample = require('./sample.js');
var sortBy = require('./sortBy.js');
var groupBy = require('./groupBy.js');
var indexBy = require('./indexBy.js');
var countBy = require('./countBy.js');
var partition = require('./partition.js');
var toArray = require('./toArray.js');
var size = require('./size.js');
var pick = require('./pick.js');
var omit = require('./omit.js');
var first = require('./first.js');
var initial = require('./initial.js');
var last = require('./last.js');
var rest = require('./rest.js');
var compact = require('./compact.js');
var flatten = require('./flatten.js');
var without = require('./without.js');
var uniq = require('./uniq.js');
var union = require('./union.js');
var intersection = require('./intersection.js');
var difference = require('./difference.js');
var unzip = require('./unzip.js');
var zip = require('./zip.js');
var object = require('./object.js');
var range = require('./range.js');
var chunk = require('./chunk.js');
var mixin = require('./mixin.js');
require('./underscore-array-methods.js');
var underscore = require('./underscore.js');

// Named Exports

exports.VERSION = _setup.VERSION;
exports.restArguments = restArguments;
exports.isObject = isObject;
exports.isNull = isNull;
exports.isUndefined = isUndefined;
exports.isBoolean = isBoolean;
exports.isElement = isElement;
exports.isString = isString;
exports.isNumber = isNumber;
exports.isDate = isDate;
exports.isRegExp = isRegExp;
exports.isError = isError;
exports.isSymbol = isSymbol;
exports.isArrayBuffer = isArrayBuffer;
exports.isDataView = isDataView;
exports.isArray = isArray;
exports.isFunction = isFunction;
exports.isArguments = isArguments;
exports.isFinite = _isFinite;
exports.isNaN = _isNaN;
exports.isTypedArray = isTypedArray;
exports.isEmpty = isEmpty;
exports.isMatch = isMatch;
exports.isEqual = isEqual;
exports.isMap = isMap;
exports.isWeakMap = isWeakMap;
exports.isSet = isSet;
exports.isWeakSet = isWeakSet;
exports.keys = keys;
exports.allKeys = allKeys;
exports.values = values;
exports.pairs = pairs;
exports.invert = invert;
exports.functions = functions;
exports.methods = functions;
exports.extend = extend;
exports.assign = extendOwn;
exports.extendOwn = extendOwn;
exports.defaults = defaults;
exports.create = create;
exports.clone = clone;
exports.tap = tap;
exports.get = get;
exports.has = has;
exports.mapObject = mapObject;
exports.identity = identity;
exports.constant = constant;
exports.noop = noop;
exports.toPath = toPath;
exports.property = property;
exports.propertyOf = propertyOf;
exports.matcher = matcher;
exports.matches = matcher;
exports.times = times;
exports.random = random;
exports.now = now;
exports.escape = _escape;
exports.unescape = _unescape;
exports.templateSettings = templateSettings;
exports.template = template;
exports.result = result;
exports.uniqueId = uniqueId;
exports.chain = chain;
exports.iteratee = iteratee;
exports.partial = partial;
exports.bind = bind;
exports.bindAll = bindAll;
exports.memoize = memoize;
exports.delay = delay;
exports.defer = defer;
exports.throttle = throttle;
exports.debounce = debounce;
exports.wrap = wrap;
exports.negate = negate;
exports.compose = compose;
exports.after = after;
exports.before = before;
exports.once = once;
exports.findKey = findKey;
exports.findIndex = findIndex;
exports.findLastIndex = findLastIndex;
exports.sortedIndex = sortedIndex;
exports.indexOf = indexOf;
exports.lastIndexOf = lastIndexOf;
exports.detect = find;
exports.find = find;
exports.findWhere = findWhere;
exports.each = each;
exports.forEach = each;
exports.collect = map;
exports.map = map;
exports.foldl = reduce;
exports.inject = reduce;
exports.reduce = reduce;
exports.foldr = reduceRight;
exports.reduceRight = reduceRight;
exports.filter = filter;
exports.select = filter;
exports.reject = reject;
exports.all = every;
exports.every = every;
exports.any = some;
exports.some = some;
exports.contains = contains;
exports.include = contains;
exports.includes = contains;
exports.invoke = invoke;
exports.pluck = pluck;
exports.where = where;
exports.max = max;
exports.min = min;
exports.shuffle = shuffle;
exports.sample = sample;
exports.sortBy = sortBy;
exports.groupBy = groupBy;
exports.indexBy = indexBy;
exports.countBy = countBy;
exports.partition = partition;
exports.toArray = toArray;
exports.size = size;
exports.pick = pick;
exports.omit = omit;
exports.first = first;
exports.head = first;
exports.take = first;
exports.initial = initial;
exports.last = last;
exports.drop = rest;
exports.rest = rest;
exports.tail = rest;
exports.compact = compact;
exports.flatten = flatten;
exports.without = without;
exports.uniq = uniq;
exports.unique = uniq;
exports.union = union;
exports.intersection = intersection;
exports.difference = difference;
exports.transpose = unzip;
exports.unzip = unzip;
exports.zip = zip;
exports.object = object;
exports.range = range;
exports.chunk = chunk;
exports.mixin = mixin;
exports.default = underscore;
